import axios from 'axios'
import qs from 'qs'

// 请求队列存储
const pendingRequests = new Map()

// 生成请求标识
const generateRequestKey = (config) => {
  const { method, url, params, data } = config
  return [method, url, qs.stringify(params), qs.stringify(data)].join('&')
}

// 取消重复请求
const removePendingRequest = (config) => {
  const requestKey = generateRequestKey(config)
  if (pendingRequests.has(requestKey)) {
    const cancelToken = pendingRequests.get(requestKey)
    cancelToken.cancel(`重复请求被取消: ${requestKey}`)
    pendingRequests.delete(requestKey)
  }
}

// 创建 axios 实例
const service = axios.create({
  baseURL: process.env.VUE_APP_BASE_API,
  timeout: 10000
})

// 请求拦截器
service.interceptors.request.use(
  (config) => {
    // 检查并取消重复请求
    removePendingRequest(config)
    
    // 创建取消令牌
    const source = axios.CancelToken.source()
    config.cancelToken = source.token
    
    // 存储请求标识和取消令牌
    const requestKey = generateRequestKey(config)
    pendingRequests.set(requestKey, source)
    
    // 添加 token
    const token = localStorage.getItem('token')
    if (token) {
      config.headers['Authorization'] = `Bearer ${token}`
    }
    
    return config
  },
  (error) => {
    return Promise.reject(error)
  }
)

// 响应拦截器
service.interceptors.response.use(
  (response) => {
    // 请求完成后从队列中移除
    const requestKey = generateRequestKey(response.config)
    pendingRequests.delete(requestKey)
    
    const { code, data, message } = response.data
    
    // 处理业务错误
    if (code !== 200) {
      return Promise.reject(new Error(message || '请求失败'))
    }
    
    return data
  },
  (error) => {
    // 如果是取消请求的错误,直接返回
    if (axios.isCancel(error)) {
      return Promise.reject(error)
    }
    
    // 从队列中移除失败的请求
    if (error.config) {
      const requestKey = generateRequestKey(error.config)
      pendingRequests.delete(requestKey)
    }
    
    // 处理错误
    if (error.response) {
      switch (error.response.status) {
        case 401:
          // 处理未授权
          break
        case 403:
          // 处理权限不足
          break
        case 404:
          // 处理资源未找到
          break
        default:
          // 处理其他错误
      }
    }
    
    return Promise.reject(error)
  }
)

// 提前声明导出对象
export const request = (config) => {
  return service(config)
}

/**
 * GET请求 - 参数跟在URL后面
 * @param {string} url 请求地址
 * @param {object} params URL参数
 * @returns {Promise}
 */
export const getWithParams = (url, params) => {
  const queryString = params ? `?${qs.stringify(params)}` : ''
  return service({
    method: 'get',
    url: `${url}${queryString}`
  })
}

/**
 * GET请求 - 参数作为config.params传递
 * @param {string} url 请求地址
 * @param {object} params 请求参数
 * @returns {Promise}
 */
export const get = (url, params) => {
  return service({
    method: 'get',
    url,
    params
  })
}

/**
 * POST请求 - JSON格式
 * @param {string} url 请求地址
 * @param {object} data 请求数据
 * @returns {Promise}
 */
export const post = (url, data) => {
  return service({
    method: 'post',
    url,
    headers: {
      'Content-Type': 'application/json'
    },
    data
  })
}

/**
 * POST请求 - 表单格式
 * @param {string} url 请求地址
 * @param {object} data 表单数据
 * @param {object} options 额外配置选项
 * @returns {Promise}
 */
export const postForm = (url, data, options = {}) => {
  return service({
    url,
    method: 'post',
    headers: {
      'Content-Type': 'application/x-www-form-urlencoded'
    },
    data: qs.stringify(data),
    ...options
  })
}

/**
 * POST请求 - FormData格式（用于文件上传）
 * @param {string} url 请求地址
 * @param {FormData} formData FormData对象
 * @param {object} options 额外配置选项
 * @returns {Promise}
 */
export const postFormData = (url, formData, options = {}) => {
  return service({
    url,
    method: 'post',
    headers: {
      'Content-Type': 'multipart/form-data'
    },
    data: formData,
    ...options
  })
}

export const put = (url, data) => {
  return service({
    method: 'put',
    url,
    data
  })
}

export const deleteRequest = (url, data) => {
  return service({
    method: 'delete',
    url,
    data
  })
}

// 默认导出request方法
export default request 