<!-- 返回主页按钮组件 -->
<template>
  <button
    class="text-white flex items-center space-x-2 !rounded-button whitespace-nowrap hover:opacity-80 cursor-pointer p-2"
    @click="goHome"
  >
    <i class="fas fa-home text-lg"></i>
    <span>{{ buttonText }}</span>
  </button>
</template>

<script>
import storage from '@/utils/storage';
export default {
  name: 'HomeButton',
  props: {
    // 按钮文本
    buttonText: {
      type: String,
      default: '返回主页'
    }
  },
  data() {
    return {
      // 上次点击时间，用于防止快速连续点击
      lastClickTime: 0
    };
  },
  methods: {
    // 返回主页
    async goHome() {
      const now = new Date().getTime();
      // 防止快速连续点击（300ms内）
      if (now - this.lastClickTime < 300) {
        console.log("点击过于频繁，忽略此次点击");
        return;
      }
      this.lastClickTime = now;
      console.log("返回主页");
      // 发送点击事件
      this.$emit('click');
      // 跳转到主页
      const userInfo = await storage.local.get('userInfo');
      this.$router.push({
                name: "actionType",
         params: { data: {...userInfo } },
      });
    }
  }
};
</script> 