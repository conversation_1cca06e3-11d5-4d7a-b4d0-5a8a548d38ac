<!-- 重置密码弹出框组件 -->
<template>
  <el-dialog
    :title="dialogTitle"
    :visible="dialogVisible"
    width="30%"
    custom-class="reset-password-dialog"
    @close="closeDialog"
  >
  <SoftKeyboard :autoInitialize="false"></SoftKeyboard>
    <el-form
      :model="form"
      :rules="rules"
      ref="resetPasswordForm"
      label-width="180px"
    >
      <!-- 新密码输入框 -->
      <el-form-item label="新密码:" prop="password">
        <el-input
          v-model="form.password"
          type="password"
          placeholder="请输入新密码"
          show-password
        >
        </el-input>
      </el-form-item>

      <!-- 确认密码输入框 -->
      <el-form-item label="再次输入新密码:" prop="confirmPassword">
        <el-input
          v-model="form.confirmPassword"
          type="password"
          placeholder="请再次输入新密码"
          show-password
        >
        </el-input>
      </el-form-item>
    </el-form>

    <span slot="footer" class="dialog-footer">
      <el-button @click="closeDialog">取消</el-button>
      <el-button type="primary" @click="submitForm">确认</el-button>
    </span>
  </el-dialog>
</template>

<script>
import SoftKeyboard from "@/components/SoftKeyboard.vue";
export default {
  name: "ResetPasswordDialog",
  components:{
    SoftKeyboard
  },
  props: {
    // 对话框标题
    dialogTitle: {
      type: String,
      default: "重置密码",
    },
    // 对话框可见性
    dialogVisible: {
      type: Boolean,
      default: false,
    },
    // 用户数据
    userData: {
      type: Object,
      default: () => ({}),
    },
  },
  data() {
    // 密码确认验证
    // 定义密码确认校验规则
    const validateConfirmPassword = (rule, value, callback) => {
      if (value === '') {
        callback(new Error('请再次输入密码'));
      } else if (value !== this.form.password) {
        callback(new Error('两次输入密码不一致'));
      } else {
        callback();
      }
    };

    return {
      form: {
        password: "",
        confirmPassword: "",
      },
      rules: {
        password: [
          {required: true, message: '请输入新密码', trigger: 'blur'}
        ],
        confirmPassword: [
          {required: true, message: '请再次输入新密码', trigger: 'blur'},
          { validator: validateConfirmPassword, trigger: "blur" },
        ],
      },
      sysconfig: {},
    };
  },
  methods: {
    // 提交表单
    submitForm() {
      this.$refs.resetPasswordForm.validate((valid) => {
        if (valid) {
          // 提交重置密码信息
          this.$emit("submit", {
            // account: this.userData.account,
            password: this.form.password,
            id: this.userData.id,
          });
          // 清空表单并关闭对话框
          this.clearForm();
        } else {
          return false;
        }
      });
    },
    // 清空表单
    clearForm() {
      this.form.password = "";
      this.form.confirmPassword = "";
      this.$refs.resetPasswordForm.clearValidate();
    },
    // 关闭对话框
    closeDialog() {
      this.clearForm();
      this.$emit("update:dialogVisible", false);
    },
  },
};
</script>

<style scoped>
/* 自定义对话框样式 */
.reset-password-dialog {
  border-radius: 12px;
  overflow: hidden;
}

:deep(.el-input__inner) {
  background-color: rgba(0, 0, 0, 0.3) !important;
  border-color: rgba(65, 105, 225, 0.5) !important;
  color: #fff !important;
  transition: all 0.3s ease !important;
}

:deep(.el-input__inner:focus) {
  border-color: rgba(65, 105, 225, 0.8) !important;
  box-shadow: 0 0 10px rgba(65, 105, 225, 0.3) !important;
}
</style> 