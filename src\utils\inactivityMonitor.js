/**
 * 页面活动监控工具
 * 监控用户交互，在指定时间内无交互时执行回调
 */

class InactivityMonitor {
  constructor() {
    this.timeout = null;
    this.warningTimeout = null;
    this.timeoutDuration = 3000; // 默认5分钟 (300000毫秒)
    this.warningDuration = 10000; // 默认在超时前30秒显示警告
    this.callback = null;
    this.warningCallback = null;
    this.isEnabled = false;
    this.lastActivity = Date.now();
    
    // 节流相关属性
    this.throttleDelay = 100; // 节流延迟时间（毫秒）
    this.throttleTimer = null;
    
    // 绑定方法到实例
    this.resetTimer = this.resetTimer.bind(this);
    this.handleActivity = this.handleActivity.bind(this);
    this.handleInactivity = this.handleInactivity.bind(this);
    this.handleWarning = this.handleWarning.bind(this);
  }

  /**
   * 设置超时时间
   * @param {Number} duration - 超时时间(毫秒)
   */
  setTimeoutDuration(duration) {
    if (typeof duration !== 'number' || duration <= 0) {
      console.error('超时时间必须是一个正数');
      return;
    }
    
    this.timeoutDuration = duration;
    console.log(`超时时间已设置为${duration/1000}秒`);
    
    // 如果监控已启动，重置计时器以应用新的超时时间
    if (this.isEnabled) {
      this.resetTimer();
    }
  }

  /**
   * 启动监控
   * @param {Function} callback - 无活动时执行的回调函数
   * @param {Number} duration - 无活动超时时间(毫秒)，默认5分钟
   * @param {Function} warningCallback - 警告回调函数（可选）
   * @param {Number} warningTime - 警告显示时间（倒计时开始前，默认30秒）
   */
  start(callback, duration = 300000, warningCallback = null, warningTime = 30000) {
    if (this.isEnabled) {
      this.stop(); // 如果已经启动，先停止之前的监控
    }
    
    this.timeoutDuration = duration;
    this.warningDuration = warningTime;
    this.callback = callback;
    this.warningCallback = warningCallback;
    this.isEnabled = true;
    this.lastActivity = Date.now();
    
    // 添加事件监听器
    this.addEventListeners();
    
    // 启动计时器
    this.resetTimer();
    
    console.log(`活动监控已启动，${duration/1000}秒无活动将执行指定操作`);
  }

  /**
   * 停止监控
   */
  stop() {
    if (!this.isEnabled) return;
    
    this.isEnabled = false;
    this.removeEventListeners();
    
    if (this.timeout) {
      clearTimeout(this.timeout);
      this.timeout = null;
    }
    
    if (this.warningTimeout) {
      clearTimeout(this.warningTimeout);
      this.warningTimeout = null;
    }
    
    if (this.throttleTimer) {
      clearTimeout(this.throttleTimer);
      this.throttleTimer = null;
    }
    
    console.log('活动监控已停止');
  }

  /**
   * 处理用户活动（使用节流）
   */
  handleActivity() {
    if (!this.isEnabled) return;
    
    // 使用节流控制重置频率
    if (!this.throttleTimer) {
      this.throttleTimer = setTimeout(() => {
        this.lastActivity = Date.now();
        this.resetTimer();
        this.throttleTimer = null;
      }, this.throttleDelay);
    }
  }

  /**
   * 重置计时器
   */
  resetTimer() {
    if (!this.isEnabled) return;
    
    if (this.timeout) {
      clearTimeout(this.timeout);
    }
    
    if (this.warningTimeout) {
      clearTimeout(this.warningTimeout);
    }
    
    // 设置警告超时（如果设置了警告回调）
    if (this.warningCallback && typeof this.warningCallback === 'function') {
      const warningDelay = this.timeoutDuration - this.warningDuration;
      this.warningTimeout = setTimeout(this.handleWarning, warningDelay);
    }
    
    // 设置主超时
    this.timeout = setTimeout(this.handleInactivity, this.timeoutDuration);
  }

  /**
   * 处理警告状态
   */
  handleWarning() {
    if (this.warningCallback && typeof this.warningCallback === 'function') {
      this.warningCallback(Math.floor(this.warningDuration / 1000));
    }
  }

  /**
   * 处理无活动状态
   */
  handleInactivity() {
    if (this.callback && typeof this.callback === 'function') {
      this.callback();
    }
  }

  /**
   * 获取自上次活动后经过的时间（毫秒）
   */
  getIdleTime() {
    return Date.now() - this.lastActivity;
  }

  /**
   * 添加事件监听器
   */
  addEventListeners() {
    // 监听用户交互事件
    document.addEventListener('mousedown', this.handleActivity);
    document.addEventListener('keydown', this.handleActivity);
    document.addEventListener('mousemove', this.handleActivity);
    document.addEventListener('touchstart', this.handleActivity);
    document.addEventListener('click', this.handleActivity);
    document.addEventListener('scroll', this.handleActivity);
  }

  /**
   * 移除事件监听器
   */
  removeEventListeners() {
    document.removeEventListener('mousedown', this.handleActivity);
    document.removeEventListener('keydown', this.handleActivity);
    document.removeEventListener('mousemove', this.handleActivity);
    document.removeEventListener('touchstart', this.handleActivity);
    document.removeEventListener('click', this.handleActivity);
    document.removeEventListener('scroll', this.handleActivity);
  }
}

// 创建单例实例
const inactivityMonitor = new InactivityMonitor();

export default inactivityMonitor; 