<!-- 排班管理组件 -->
<template>
  <div>
    <!-- 排班管理页面 -->
    
    <el-dialog
      :title="`${scheduleInfo.name || '班表'} - 排班管理`"
      :visible.sync="dialogVisibleValue"
      width="80%"
      fullscreen
      custom-class="schedule-manage-dialog"
    >
      <!-- 搜索和添加区域 -->
      <div class="top-actions-container">
        <div class="left-actions">
          <!-- 美化离线模式提示 -->
          <el-button
            type="primary"
            icon="el-icon-plus"
            @click="handleAdd" 
            class="new-schedule-btn"
          >
            添加排班
          </el-button>

          <el-button
            type="primary"
            icon="el-icon-plus"
            @click="handleBatchDelete" 
            class="new-schedule-btn"
          >
            批量删除排班
          </el-button>

          <el-button
            type="primary"
            icon="el-icon-plus"
            @click="handleImport" 
            class="new-schedule-btn"
          >
          导入排班信息
          </el-button>


        </div>
        <div class="right-actions">
          <!--分页信息-->
          <div class="pagination-info">
            <span>当前第 {{ currentPage }} 页</span>
            <span class="divider">|</span>
            <span>共 {{ Math.ceil(totalItems / pageSize) || 0 }} 页</span>
            <span class="divider">|</span>
            <span>总计 {{ totalItems }} 条</span>
          </div>
        </div>
      </div>

      <!-- 表格容器 -->
      <div 
        class="card-list-container" 
        ref="cardListContainer" 
        @scroll="handleScroll"
        @touchstart="handleTouchStart"
        @touchmove="handleTouchMove"
        @touchend="handleTouchEnd"
      >
        <div class="card-grid">
          <div 
            v-for="item in tableData" 
            :key="item.id"
            class="schedule-card"
          >
            <el-checkbox
              v-model="selectedIds"
              :label="item.id"
              class="card-checkbox"
              style="margin-bottom: 8px; margin-left: 12px; padding-right: 12px;"
            >
              <span style="font-size: 0;"></span>
            </el-checkbox>
            <div class="schedule-card-content">
              <div class="info-row">
                <div class="info-label">用户名：</div>
                <div class="info-value">{{ item.userName }}</div>
              </div>
              <div class="info-row">
                <div class="info-label">姓名：</div>
                <div class="info-value">{{ item.displayName }}</div>
              </div>
              <div class="info-row">
                <div class="info-label">手机号：</div>
                <div class="info-value">{{ item.mobile }}</div>
              </div>
              <div class="info-row">
                <div class="info-label">公司：</div>
                <div class="info-value" :title="item.companyName">{{ item.companyName }}</div>
              </div>
              <div class="info-row">
                <div class="info-label">工作部门：</div>
                <div class="info-value" :title="item.workDeptName">{{ item.workDeptName }}</div>
              </div>
              <div class="info-row">
                <div class="info-label">工作日期：</div>
                <div class="info-value">{{ item.workDate }}</div>
              </div>
              <div class="info-row">
                <div class="info-label">上班时间：</div>
                <div class="info-value">{{ item.workTime }}</div>
              </div>
              <div class="info-row">
                <div class="info-label">下班时间：</div>
                <div class="info-value">
                  {{ item.workOffTime }}
                  <el-tag v-if="item.crossDay" size="mini" type="warning">次日</el-tag>
                </div>
              </div>
            </div>
            <div class="schedule-card-actions">
              <el-button
                size="mini"
                type="primary"
                @click="handleEdit(item)"
                class="action-btn"
                >编辑</el-button
              >
              <el-button
                size="mini"
                type="danger"
                @click="handleDelete(item)"
                class="action-btn"
                >删除</el-button
              >
            </div>
          </div>
        </div>
        
        <!-- 加载更多指示器 -->
        <div v-if="isLoading" class="loading-indicator">
          <i class="el-icon-loading"></i>
          <span>加载中...</span>
        </div>
        
        <!-- 全部加载完毕提示 -->
        <div v-if="allDataLoaded && tableData.length > 0" class="load-all-indicator">
          <span>——— 已加载全部数据 ———</span>
        </div>
        
        <!-- 没有数据提示 -->
        <div v-if="!isLoading && tableData.length === 0" class="no-data-indicator">
          <i class="el-icon-warning-outline"></i>
          <span>暂无数据</span>
        </div>
      </div>

      <!-- 回到顶部按钮 -->
      <div 
        class="scroll-to-top-btn"
        :class="{ 'visible': showScrollTopBtn }"
        @click="scrollToTop"
      >
        <i class="el-icon-arrow-up"></i>
      </div>

      <!-- 分页 -->
      <!-- <div class="pagination-container mt-6 flex justify-end" style="margin-top: -10px;">
        <el-pagination
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
          :current-page="currentPage"
          :page-sizes="[10, 20, 30, 50]"
          :page-size="pageSize"
          layout="total, sizes, prev, pager, next, jumper"
          :total="totalItems"
        >
        </el-pagination>
      </div> -->

      <!-- 底部按钮 -->
      <!-- <div slot="footer" class="dialog-footer">
        <el-button @click="closeDialog">关闭</el-button>
      </div> -->
    </el-dialog>

    <!-- 添加/编辑排班对话框 -->
    <el-dialog
      :title="isEdit ? '编辑排班' : '添加排班'"
      :visible.sync="formDialogVisible"
      width="50%"
      custom-class="schedule-form-dialog"
      @close="resetForm"
    >

      <el-form
        :model="form"
        :rules="rules"
        ref="scheduleForm"
        label-width="100px"
      >
        <el-form-item label="选择员工" prop="personIds">
          <el-select
            v-mobile-select
            filterable
            v-model="form.personIds"
            :disabled="isEdit"
            style="width: 100%"
            
            placeholder="选择 (可多选)"
            multiple
            :loading="loading"
          >
            <el-option v-if="loading" disabled value="" label="数据加载中...">
            </el-option>
            <el-option
              v-for="item in employeeOptions"
              :key="item.id"
              :label="`${item.account} ${item.realName} ${item.mobile}`"
              :value="item.id"
              multiple
            ></el-option>
          </el-select>
        </el-form-item>

        <el-form-item label="工作日期" prop="workDate">
          <el-date-picker
            v-model="form.workDate"
            type="date"
            placeholder="时间选择（年月日-年月日）"
            value-format="yyyy-MM-dd"
            :picker-options="datePickerOptions"
            style="width: 100%"
          >
          </el-date-picker>
        </el-form-item>

        <el-form-item label="上班时间" prop="workTime">
          <el-time-picker
            v-model="form.workTime"
            placeholder="时间选择（时分）"
            value-format="HH:mm"
            format="HH:mm"
            style="width: 100%"
          >
          </el-time-picker>
        </el-form-item>

        <el-form-item label="是否次日" prop="crossDay">
          <el-radio-group
            v-model="form.crossDay"
            @change="
              () => {
                this.$refs.scheduleForm.validateField('workOffTime');
              }
            "
          >
            <el-radio :label="true">是</el-radio>
            <el-radio :label="false">否</el-radio>
          </el-radio-group>
        </el-form-item>

        <el-form-item label="下班时间" prop="workOffTime">
          <el-time-picker
            v-model="form.workOffTime"
            placeholder="时间选择（时分）"
            value-format="HH:mm"
            format="HH:mm"
            style="width: 100%"
          >
          </el-time-picker>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="formDialogVisible = false">取 消</el-button>
        <el-button type="primary" @click="submitForm">确 定</el-button>
      </div>
    </el-dialog>

    <!-- 删除确认对话框 -->
    <el-dialog
      title="确认删除"
      :visible.sync="deleteDialogVisible"
      width="30%"
      custom-class="delete-dialog"
    >
      <span v-if="deleteItem.ids">确定要删除选中的{{deleteItem.ids.length}}条排班记录吗？此操作不可恢复。</span>
      <span v-else>确定要删除该排班记录吗？此操作不可恢复。</span>
      <span slot="footer" class="dialog-footer">
        <el-button @click="deleteDialogVisible = false">取 消</el-button>
        <el-button type="danger" @click="confirmDelete">确 定</el-button>
      </span>
    </el-dialog>

    <!--导入排班信息-->
    <el-dialog
      title="导入排班信息"
      :visible.sync="importDialogVisible"
      width="40%"
      custom-class="import-dialog"
    >
      <div class="import-container">
        <div class="import-btn-container text-center mb-4">
          <el-button type="primary" @click="selectImportFile" class="import-select-btn">
            <i class="el-icon-folder-opened mr-2"></i>选择文件
          </el-button>
          <div v-if="selectedFilePath" class="selected-file-path mt-3">
            <p class="text-truncate">当前选择: {{ selectedFilePath }}</p>
          </div>
        </div>

        <div class="import-tips mt-4">
          <p class="text-warning mb-2">
            <i class="el-icon-warning mr-1"></i>导入说明：
          </p>
          <ul class="ml-4">
            <li>1. 请选择Excel文件(.xlsx/.xls)</li>
            <li>2. 文件必须包含用户名、姓名、手机号等必要字段</li>
            <li>3. 导入数据将仅在本地存储，连接网络后将自动同步</li>
            <li>
              4. 路径不能用中文
            </li>
          </ul>
        </div>
      </div>
      <span slot="footer" class="dialog-footer">
        <el-button @click="importDialogVisible = false">取 消</el-button>
        <el-button type="primary" @click="importUsers" :disabled="!selectedFilePath">导 入</el-button>
      </span>
    </el-dialog>
      <!--导入排班信息-->
  </div>
</template>

<script>
export default {
  name: "ScheduleManage",
  inject: ["speak", "websocketService", "websocketMsg", "speakSync"],
  props: {
    // 对话框是否可见
    dialogVisible: {
      type: Boolean,
      default: false,
    },
    // 班表信息
    scheduleInfo: {
      type: Object,
      default: () => ({}),
    },
  },
  mounted() {
//     handleBatchDelete(){
// //
//     },
    // 如果弹窗一开始就是显示状态，则获取数据
    if (this.dialogVisible) {
      this.resetAndReload();
    }
    
    // 添加滚动事件监听
    this.$nextTick(() => {
      if (this.$refs.cardListContainer) {
        // 使用passive选项优化滚动事件监听，提高滚动性能
        this.$refs.cardListContainer.addEventListener('scroll', this.toggleScrollTopButton, { passive: true });
        
        // 添加触摸事件监听
        this.$refs.cardListContainer.addEventListener('touchstart', this.handleTouchStart, { passive: true });
        this.$refs.cardListContainer.addEventListener('touchmove', this.handleTouchMove, { passive: false });
        this.$refs.cardListContainer.addEventListener('touchend', this.handleTouchEnd, { passive: true });
      }
    });
  },
  beforeDestroy() {
    // 移除滚动事件监听
    if (this.$refs.cardListContainer) {
      this.$refs.cardListContainer.removeEventListener('scroll', this.toggleScrollTopButton);
      
      // 移除触摸事件监听
      this.$refs.cardListContainer.removeEventListener('touchstart', this.handleTouchStart);
      this.$refs.cardListContainer.removeEventListener('touchmove', this.handleTouchMove);
      this.$refs.cardListContainer.removeEventListener('touchend', this.handleTouchEnd);
      
      // 清除滚动计时器
      if (this.scrollTimer) {
        cancelAnimationFrame(this.scrollTimer);
        this.scrollTimer = null;
      }
    }
  },
  data() {
    // 自定义验证器：确保下班时间不早于上班时间
    const validateworkOffTime = (rule, value, callback) => {
      if (!value) {
        callback(new Error("请选择下班时间"));
        return;
      }

      if (!this.form.workTime) {
        callback();
        return;
      }

      const startTime = this.form.workTime.split(":");
      const endTime = value.split(":");

      const startMinutes = parseInt(startTime[0]) * 60 + parseInt(startTime[1]);
      const endMinutes = parseInt(endTime[0]) * 60 + parseInt(endTime[1]);

      if (endMinutes <= startMinutes && !this.form.crossDay) {
        callback(new Error("下班时间不能早于或等于上班时间"));
      } else {
        callback();
      }
    };

    // 自定义验证器：确保工作日期不早于今天
    const validateWorkDate = (rule, value, callback) => {
      if (!value) {
        callback(new Error("请选择工作日期"));
        return;
      }

      const today = new Date();
      today.setHours(0, 0, 0, 0);

      const selectedDate = new Date(value);
      selectedDate.setHours(0, 0, 0, 0);

      if (selectedDate < today) {
        callback(new Error("工作日期不能早于今天"));
      } else {
        callback();
      }
    };
    return {
      importDialogVisible: false,
      fileList: [],
      selectedFile: null,
      selectedFilePath: "",
      loading: false, // 员工数据加载状态
      datePickerOptions: {
        disabledDate(time) {
          return time.getTime() < Date.now() - 8.64e7; // 禁用今天之前的日期
        },
      },
      // 当前页
      currentPage: 1,
      // 每页显示条目数
      pageSize: 20,
      // 总条目数
      totalItems: 0,
      // 是否编辑状态
      isEdit: false,
      // 添加/编辑排班表单对话框是否可见
      formDialogVisible: false,
      // 删除确认对话框是否可见
      deleteDialogVisible: false,
      // 待删除的项
      deleteItem: {},
      // 添加/编辑排班表单
      form: {
        personIds: [],
        crossDay: false,
        workDate: "",
        workTime: "",
        workOffTime: "",
      },
      // 表单验证规则
      rules: {
        personIds: [
          { required: true, message: "请选择员工", trigger: "change" },
        ],
        workDate: [
          { required: true, message: "请选择工作日期", trigger: "change" },
          { validator: validateWorkDate, trigger: "change" },
        ],
        workTime: [
          { required: true, message: "请选择上班时间", trigger: "change" },
        ],
        crossDay: [
          { required: true, message: "是否次日下班", trigger: "change" },
        ],
        workOffTime: [
          { required: true, message: "请选择下班时间", trigger: "change" },
          { validator: validateworkOffTime, trigger: "change" },
        ],
      },

      // 员工选项（模拟数据）
      employeeOptions: [
  
      ],
      // 排班数据
      tableData: [
 
      ],
      isLoading: false,
      allDataLoaded: false,
      showScrollTopBtn: false,
      selectedIds: [], // 新增：多选选中的排班id
      scrollTimer: null,       // 滚动防抖计时器
      scrollThreshold: 100,    // 滚动加载阈值
      touchStartY: 0,          // 触摸开始位置
    };
  },
  computed: {
    // 对话框可见性计算属性
    dialogVisibleValue: {
      get() {
        return this.dialogVisible;
      },
      set(value) {
        if (value === true) {
          // 当弹窗打开时，调用getdata方法获取数据
          this.getdata();
        }
        this.$emit("update:dialogVisible", value);
      },
    },
    // 根据搜索条件筛选的表格数据
  },
  watch: {
    // 监听scheduleInfo变化
    scheduleInfo: {
      handler(newVal) {
        if (newVal && Object.keys(newVal).length > 0) {
          // 可以根据传入的班表信息加载对应排班数据
          console.log("加载班表信息:", newVal);
          // TODO: 实际项目中，这里应该调用API获取排班数据
        }
      },
      immediate: true,
    },
    // 监听对话框可见性变化
    dialogVisible: {
      handler(newVal) {
        if (newVal === true) {
          console.log("对话框打开，获取数据");
          this.$nextTick(() => {
            this.getdata();
          });
        }
      },
      immediate: true,
    },
    websocketMsg: {
      handler(newMessage) {
        console.log("收到新的WebSocket排班消息:", newMessage);
        if (newMessage) {
          this.handleSchedulingMessages(newMessage);
        }
      },
      deep: true,
      immediate: true,
    },
  },
  created() {
    // 初始化数据
  },
  methods: {
    handleSchedulingMessages(message) {
      if (!message) return;
      // 根据消息类型处理不同的情况
      if (message && message.messages) {
        switch (message.messages.type) {
          case 20201: //获取绑定的部门ID列表
            if (message.messages.data && message.messages.data.code === 1) {
              var orgs = message.messages.data.data;
               this.getUserFindList(orgs)
            } else {
              this.speak(message.messages.data.msg);
            }
            break;
            case 20202: //获取部门用户列表
            if (message.messages.data && message.messages.data.code === 1) {
              console.log("获取部门用户列表",message.messages.data.list);
               this.employeeOptions = message.messages.data.list;
               
            } else {
              this.speak(message.messages.data.msg);
            }
            break;  

          case 20070: //排班管理分页
            this.isLoading = false;
            if (message.messages.data && message.messages.data.code === 1) {
              const newData = message.messages.data.list || [];
              this.totalItems = message.messages.data.total || 0;
              
              if (this.currentPage === 1) {
                // 首次加载或刷新
                this.tableData = newData;
              } else if (newData.length > 0) {
                // 追加数据
                this.tableData = [...this.tableData, ...newData];
              }
              
              // 判断是否已加载所有数据
              this.allDataLoaded = this.tableData.length >= this.totalItems || newData.length === 0;
            } else {
              this.speak(message.messages.data.msg);
            }
            break;
          case 20071: //新增排班 编辑排班
            if (message.messages.data && message.messages.data.code === 1) {
              this.formDialogVisible = false;
              this.resetAndReload();
              this.$message({
                type: "success",
                message:
                  "排班保存成功！当前为离线模式，数据仅本地存储，联网后用户信息将自动同步平台!",
              });
            } else {
              this.speak(message.messages.data.msg);
            }
            break;
            case 20072: //编辑排班
            if (message.messages.data && message.messages.data.code === 1) {
              this.formDialogVisible = false;
              this.resetAndReload();
              this.$message({
                type: "success",
                message:
                  "排班修改成功！当前为离线模式，数据仅本地存储，联网后用户信息将自动同步平台!",
              });
            } else {
              this.speak(message.messages.data.msg);
            }
            break;  
          case 20074: //批量删除排班
            if (message.messages.data && message.messages.data.code === 1) {
              this.$message({
                type: "success",
                message:
                  "删除排班成功！当前为离线模式，数据仅本地存储，联网后用户信息将自动同步平台!",
              });
              this.deleteDialogVisible = false;
              this.selectedIds = []; // 新增：清空多选
              this.resetAndReload();
            } else {
              this.speak(message.messages.data.msg);
            }
            break;
            case 20073: //删除排班
            if (message.messages.data && message.messages.data.code === 1) {
              this.$message({
                type: "success",
                message:
                  "删除排班成功！当前为离线模式，数据仅本地存储，联网后用户信息将自动同步平台!",
              });
              this.deleteDialogVisible = false;
              this.resetAndReload();
            } else {
              this.speak(message.messages.data.msg);
            }
            break;


            case 20075: //导入成功
            if (message.messages.data && message.messages.data.code === 1) {
              this.$loading().close();
              
              var msgString = message?.messages?.data?.failedRecords ? message.messages.data.failedRecords.map(item => item.error) : []
              if(msgString.length === 0){
                this.$message({
                type: "success",
                message:"导入排班成功！当前为离线模式，数据仅本地存储，联网后用户信息将自动同步平台!",
              });
              }else{
                this.$message({
                type: "error",
                message:msgString.join(","),
              });
              }
              this.importDialogVisible = false;
              this.resetAndReload();
            } else {
              this.$loading().close();
              this.speak(message.messages.data.msg);
              this.importDialogVisible = false;
            }
            break;


            
          default:
        }
      }
    },

    getdata() {
      this.isLoading = true;
      const order = {
        type: 10070,
        id: "getScheduleData",
        data: {
          current: this.currentPage,
          limit: this.pageSize,
          scheduleId: this.scheduleInfo.id, // 添加班组ID参数
        },
      };
      this.websocketService.send(order);
    },

    getUserBindDeptIds() {
      const order = {
        type: 10201,
        id: "getUserBindDeptIds",
        data: {
        },
      };
      this.websocketService.send(order);
    },

    getUserFindList(orgIds) {
      const order = {
        type: 10202,
        id: "getUserFindList",
        data: {
          orgIds
        },
      };
      this.websocketService.send(order);
    },

    // 处理WebSocket返回的排班数据
    handleImport() {
      this.importDialogVisible = true;
      this.fileList = [];
      this.selectedFile = null;
      this.selectedFilePath = "";
    },
       // 选择导入文件
    selectImportFile() {
      // 使用Electron的dialog模块选择文件
      if (window.electronAPI) {
        window.electronAPI.selectImportFile({
          title: '选择用户数据文件',
          filters: [
            { name: 'Excel文件', extensions: ['xlsx', 'xls'] },
            // { name: 'CSV文件', extensions: ['csv'] },
            // { name: '所有文件', extensions: ['*'] }
          ],
          properties: ['openFile']
        }).then(result => {
          if (!result.canceled && result.filePaths && result.filePaths.length > 0) {
            this.selectedFilePath = result.filePaths[0];
            console.log('选择的文件路径:', this.selectedFilePath);
          }
        }).catch(err => {
          console.error('选择文件出错:', err);
          this.$message.error('选择文件失败，请重试');
        });
      } else {
        console.error('无法访问Electron API');
        this.$message.error('当前环境不支持文件选择功能');
      }
    },
    // 添加排班
    handleAdd() {
      this.getUserBindDeptIds();
      this.isEdit = false;
      this.resetForm();
      this.formDialogVisible = true;
    },
    // 编辑排班
    handleEdit(row) {
      this.getUserBindDeptIds();
      this.isEdit = true;
      // 设置表单数据
      this.form = {
        ...row,
        personIds: [row.personId],
      };

      this.formDialogVisible = true;
    },
    // 解析工作日期范围字符串为日期对象数组（示例实现）
    parseWorkDateRange(rangeStr) {
      if (!rangeStr) return [];

      // 假设格式为 "2023-06-01 至 2023-06-07"
      const dates = rangeStr.split(" 至 ");
      if (dates.length !== 2) return [];

      return [new Date(dates[0].trim()), new Date(dates[1].trim())];
    },
    // 解析时间字符串为时间对象（示例实现）
    parseTimeString(timeStr) {
      if (!timeStr) return "";

      // 假设格式为 "08:00:00"
      const [hours, minutes, seconds] = timeStr.split(":").map(Number);

      // 创建当天的日期对象，并设置时间
      const date = new Date();
      date.setHours(hours, minutes, seconds);

      return date;
    },
    // 删除排班
    handleDelete(row) {
      this.deleteItem = row;
      this.deleteDialogVisible = true;
    },
    // 确认删除
    confirmDelete() {
      if (this.deleteItem.ids) {
        // 批量删除
        const order = {
          type: 10074,
          id: 'batchDelete',
          data: { ids: this.deleteItem.ids },
        };
        this.websocketService.send(order);
      } else {
        // 单条删除
        const order = {
          type: 10073,
          id: "confirmDelete",
          data: {
            id: this.deleteItem.id,
          },
        };
        this.websocketService.send(order);
      }
    },
    // 更新总条目数
    updateTotalItems() {
      this.totalItems = this.tableData.length;
    },
    // 搜索排班
    searchSchedules() {
      // 使用计算属性 filteredTableData 自动根据 searchText 过滤表格数据
      console.log("搜索关键词:", this.searchText);
      // 更新分页信息
      this.currentPage = 1;
    },
    // 分页大小改变
    handleSizeChange(newSize) {
      this.pageSize = newSize;
      this.getdata();
    },
    // 页码改变
    handleCurrentChange(newPage) {
      this.currentPage = newPage;
      this.getdata();
    },
    // 提交表单
    submitForm() {
      this.$refs.scheduleForm.validate((valid) => {
        if (valid) {
          const formData = {};
          if (!this.isEdit) {
            let filterData = this.employeeOptions.filter((item) =>
              this.form.personIds.includes(item.id)
            );

            formData.list = filterData.map((item) => {
              return {
                groupId: item.groupId,
                personId: item.id,
                scheduleId: this.scheduleInfo.id,
                workDate: this.form.workDate,
                crossDay: this.form.crossDay,
                workOffTime: this.form.workOffTime + ":00",
                workTime: this.form.workTime + ":00",
              };
            });

            const order = {
              type: 10071,
              id: "addSchedule",
              data: {
                list: formData.list,
              },
            };
            this.websocketService.send(order);
          } else {
            formData.param = {
              scheduleId: this.scheduleInfo.id,
              id: this.form.id,
              personId: this.form.personIds[0],
              workDate: this.form.workDate,
              crossDay: this.form.crossDay,
              workOffTime:
                (this.form.workOffTime.match(/:/g) || []).length >= 2
                  ? this.form.workOffTime
                  : this.form.workOffTime + ":00",
              workTime:
                (this.form.workTime.match(/:/g) || []).length >= 2
                  ? this.form.workTime
                  : this.form.workTime + ":00",
            };
            const order = {
              type: 10072,
              id: "updateSchedule",
              data: {
                ...formData.param,
              },
            };
            this.websocketService.send(order);
          }
        }
      });
    },
  
    // 重置表单
    resetForm() {
      if (this.$refs.scheduleForm) {
        this.$refs.scheduleForm.resetFields();
      }

      this.form = {
        personIds: [],
        crossDay: false,
        workDate: "",
        workTime: "",
        workOffTime: "",
      };
    },
    // 关闭对话框
    closeDialog() {
      this.dialogVisibleValue = false;
    },
    // 添加导入排班信息方法
    handleImportSchedule() {
      this.$message({
        message: '正在准备导入排班信息...',
        type: 'info',
        duration: 2000
      });
      // 这里可以添加实际的导入逻辑
      console.log('导入排班信息');
    },
    importUsers() {
      if (!this.selectedFilePath) {
        this.$message.warning("请先选择要导入的文件");
        return;
      }

      // 显示加载中
      this.$loading({
        lock: true,
        text: "正在提交导入请求，请稍候...",
        spinner: "el-icon-loading",
        background: "rgba(0, 0, 0, 0.7)",
      });

      // 发送文件路径到后端
      let order = {
        type: 10075,
        id: "importUsers",
        data: {
          file: this.selectedFilePath,
          scheduleId: this.scheduleInfo.id
        },
      };
      
      this.websocketService.send(order);
      
      // 关闭加载提示
      // setTimeout(() => {
      //   this.$loading().close();
      //   this.$message({
      //     type: "success",
      //     message: "导入请求已发送！当前为离线模式，数据仅本地存储，联网后将自动同步。",
      //   });
      //   this.importDialogVisible = false;
      // }, 1000);
    },
    // 下载模板
    downloadTemplate() {
      // 模拟下载模板文件
      this.$message({
        type: "info",
        message: "模板下载中...",
      });

      // 实际项目中，这里应该实现一个真实的文件下载功能
      setTimeout(() => {
        this.$message({
          type: "success",
          message: "模板下载成功！",
        });
      }, 1000);
    },
    handleScroll() {
      // 使用requestAnimationFrame优化滚动性能
      if (this.scrollTimer) return;
      
      this.scrollTimer = requestAnimationFrame(() => {
        const container = this.$refs.cardListContainer;
        if (!container) return;
        
        // 滚动到距离底部一定距离时，加载更多数据
        const scrollBottom = container.scrollHeight - container.scrollTop - container.clientHeight;
        const scrollThreshold = 100; // 设置一个合理的阈值
        
        if (scrollBottom < scrollThreshold) {
          if (!this.isLoading && !this.allDataLoaded) {
            this.currentPage++;
            this.getdata();
          }
        }
        
        // 更新回到顶部按钮的可见性
        this.showScrollTopBtn = container.scrollTop > 200;
        
        this.scrollTimer = null;
      });
    },
    toggleScrollTopButton() {
      const container = this.$refs.cardListContainer;
      if (container) {
        this.showScrollTopBtn = container.scrollTop > 200;
      }
    },
    scrollToTop() {
      const container = this.$refs.cardListContainer;
      if (container) {
        // 使用动画帧优化滚动性能
        const startPosition = container.scrollTop;
        const startTime = performance.now();
        const duration = 500; // 滚动动画持续时间(ms)
        
        const animateScroll = (currentTime) => {
          const elapsedTime = currentTime - startTime;
          if (elapsedTime < duration) {
            const progress = 1 - Math.pow(1 - elapsedTime / duration, 3); // 缓动函数
            container.scrollTop = startPosition * (1 - progress);
            requestAnimationFrame(animateScroll);
          } else {
            container.scrollTop = 0;
          }
        };
        
        requestAnimationFrame(animateScroll);
      }
    },
    // 重置并重新加载数据
    resetAndReload() {
      this.currentPage = 1;
      this.tableData = [];
      this.allDataLoaded = false;
      this.getdata();
    },
    // 批量删除排班
    handleBatchDelete() {
      if (!this.selectedIds.length) {
        this.$message.warning('请先选择要删除的排班');
        return;
      }
      this.deleteItem = { ids: this.selectedIds };
      this.deleteDialogVisible = true;
    },
    // 触摸事件处理
    handleTouchStart(event) {
      this.touchStartY = event.touches[0].clientY;
    },
    handleTouchMove(event) {
      // 阻止事件冒泡，避免误触发其他元素
      if (event.cancelable) {
        event.stopPropagation();
      }
      
      const touchEndY = event.touches[0].clientY;
      const touchDelta = this.touchStartY - touchEndY;
      
      // 判断是否有显著的垂直滑动
      if (Math.abs(touchDelta) > 50) {
        // 防止触摸事件导致的页面抖动
        if (event.cancelable) {
          event.preventDefault();
        }
        
        // 下拉一定距离后快速回到顶部
        if (touchDelta < -100) {
          this.scrollToTop();
        }
      }
    },
    handleTouchEnd() {
      // 触摸结束处理
      this.touchStartY = 0;
    },
  },
};
</script>

<style scoped>
/* 离线模式提示样式 */
.offline-mode-notice {
  display: flex;
  align-items: center;
  background-color: rgba(65, 105, 225, 0.2);
  padding: 10px 20px;
  border-radius: 20px;
  border: 1px solid rgba(65, 105, 225, 0.3);
  cursor: pointer;
  transition: all 0.3s ease;
  box-shadow: 0 4px 10px rgba(65, 105, 225, 0.3);
}

.offline-mode-notice:hover {
  background-color: rgba(65, 105, 225, 0.3);
  transform: translateY(-2px);
  box-shadow: 0 6px 14px rgba(65, 105, 225, 0.4);
}

.offline-icon {
  color: #409eff;
  font-size: 18px;
  margin-right: 8px;
  animation: pulse 2s infinite;
}

.offline-text {
  color: #fff;
  font-weight: 500;
  margin-right: 8px;
}

.import-icon {
  color: #409eff;
  font-size: 16px;
}

.import-schedule-btn {
  color: rgba(255, 255, 255, 0.8) !important;
  font-size: 14px !important;
  padding: 0 !important;
}

.import-schedule-btn:hover {
  color: #fff !important;
  text-decoration: underline;
}

@keyframes pulse {
  0% {
    opacity: 1;
  }
  50% {
    opacity: 0.6;
  }
  100% {
    opacity: 1;
  }
}

/* 表格滚动容器 */
.card-list-container {
  width: 100%;
  height: calc(100vh - 200px);
  overflow: auto;
  margin-bottom: 1rem;
  border-radius: 8px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
  -webkit-overflow-scrolling: touch; /* 增强iOS滚动体验 */
  scroll-behavior: smooth; /* 平滑滚动 */
}

/* 美化表格滚动条 */
.card-list-container::-webkit-scrollbar {
  width: 16px;
  height: 16px;
}

.card-list-container::-webkit-scrollbar-track {
  background: #1a1f2c;
  border-radius: 10px;
}

.card-list-container::-webkit-scrollbar-thumb {
  background: #212840;
  border-radius: 10px;
  border: 2px solid #1a1f2c;
}

.card-list-container::-webkit-scrollbar-thumb:hover {
  background: #252e4a;
}

.card-list-container::-webkit-scrollbar-corner {
  background: #1a1f2c;
}

/* 卡片网格布局 */
.card-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
  gap: 25px; /* 增大卡片间距 */
  width: 100%;
  padding: 15px; /* 增大内边距 */
}

/* 横屏状态 - 四列布局 */
@media (min-width: 1600px) {
  .card-grid {
    grid-template-columns: repeat(4, 1fr);
  }
}

/* 中等屏幕 - 三列布局 */
@media (max-width: 1599px) and (min-width: 1200px) {
  .card-grid {
    grid-template-columns: repeat(3, 1fr);
  }
}

/* 平板状态 - 两列布局 */
@media (max-width: 1199px) and (min-width: 768px) {
  .card-grid {
    grid-template-columns: repeat(2, 1fr);
  }
}

/* 竖屏手机状态 - 单列布局 */
@media (max-width: 767px) {
  .card-grid {
    grid-template-columns: 1fr;
  }
}

/* 排班卡片样式 */
.schedule-card {
  display: flex;
  flex-direction: column;
  background-color: rgba(34, 44, 74, 0.8);
  border-radius: 12px;
  overflow: hidden;
  box-shadow: 0 4px 10px rgba(0, 0, 0, 0.3);
  backdrop-filter: blur(5px);
  border: 1px solid rgba(255, 255, 255, 0.1);
  transition: all 0.3s ease;
  touch-action: manipulation; /* 优化触摸行为 */
  -webkit-tap-highlight-color: transparent; /* 移除触摸高亮 */
}

.schedule-card:hover {
  transform: translateY(-3px);
  box-shadow: 0 8px 15px rgba(0, 0, 0, 0.4);
}

.schedule-card-content {
  padding: 20px; /* 增大内边距 */
  flex-grow: 1;
}

/* 卡片信息区 */
.info-row {
  display: flex;
  margin-bottom: 12px;
  align-items: flex-start;
  justify-content: space-between;
  min-height: 24px; /* 确保每一行有足够的高度 */
}

.info-label {
  color: rgba(255, 255, 255, 0.9);
  font-size: 14px;
  text-align: left;
}

.info-value {
  color: #ffffff;
  font-size: 14px;
  text-align: right;
  font-weight: 500;
  max-width: 60%;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

/* 卡片操作区 */
.schedule-card-actions {
  display: flex;
  justify-content: space-between;
  padding: 15px; /* 增大内边距 */
  background-color: rgba(20, 27, 47, 0.6);
}

.action-btn {
  flex: 1;
  margin: 0 5px !important;
  padding: 10px 15px !important; /* 增大按钮内边距 */
  border-radius: 4px !important;
  color: white !important;
  border: none !important;
  font-size: 14px;
  line-height: 1.2;
  height: auto;
  min-height: 42px; /* 确保触摸区域足够大 */
}

/* 美化搜索和添加按钮 */
.search-input :deep(.el-input__inner) {
  border-radius: 20px 0 0 20px !important;
  transition: all 0.3s ease;
}

.search-btn {
  border-radius: 0 20px 20px 0 !important;
  padding: 10px 20px !important;
  transition: all 0.3s ease;
}

.add-btn {
  border-radius: 20px !important;
  padding: 10px 20px !important;
  transition: all 0.3s ease;
  box-shadow: 0 4px 10px rgba(19, 206, 102, 0.3) !important;
}

.add-btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 6px 14px rgba(19, 206, 102, 0.4) !important;
}

/* 自定义对话框样式 */
:deep(.schedule-manage-dialog) {
  background-color: #1c1f37 !important;
  border: 1px solid rgba(65, 105, 225, 0.5) !important;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.5) !important;
}

:deep(.schedule-manage-dialog .el-dialog__header),
:deep(.schedule-form-dialog .el-dialog__header) {
  background-color: rgba(65, 105, 225, 0.2) !important;
  padding: 15px 20px !important;
  border-bottom: 1px solid rgba(65, 105, 225, 0.3) !important;
}

:deep(.schedule-manage-dialog .el-dialog__title),
:deep(.schedule-form-dialog .el-dialog__title) {
  color: #fff !important;
  font-weight: bold !important;
}

:deep(.schedule-manage-dialog .el-dialog__body),
:deep(.schedule-form-dialog .el-dialog__body) {
  color: #fff !important;
  padding: 20px !important;
}

:deep(.schedule-manage-dialog .el-dialog__footer),
:deep(.schedule-form-dialog .el-dialog__footer) {
  border-top: 1px solid rgba(65, 105, 225, 0.3) !important;
  padding: 15px 20px !important;
}

:deep(.el-form-item__label) {
  color: #fff !important;
}

:deep(.el-input__inner),
:deep(.el-select__input),
:deep(.el-date-editor),
:deep(.el-time-panel) {
  background-color: rgba(0, 0, 0, 0.3) !important;
  border-color: rgba(65, 105, 225, 0.5) !important;
  color: #fff !important;
  transition: all 0.3s ease !important;
}

:deep(.el-date-editor) {
  width: 100% !important;
}

:deep(.el-input__inner:focus),
:deep(.el-select__input:focus) {
  border-color: rgba(65, 105, 225, 0.8) !important;
  box-shadow: 0 0 10px rgba(65, 105, 225, 0.3) !important;
}

/* 自定义日期选择器和时间选择器样式 */
:deep(.el-date-picker),
:deep(.el-time-picker) {
  background-color: #1c1f37 !important;
  border: 1px solid rgba(65, 105, 225, 0.5) !important;
  color: #fff !important;
}

:deep(.el-picker-panel__content) {
  color: #fff !important;
}

:deep(.el-date-table td.available:hover),
:deep(.el-time-panel__content .el-time-spinner__item:hover) {
  color: #409eff !important;
}

:deep(.el-date-table td.current:not(.disabled)) {
  color: #fff !important;
  background-color: #409eff !important;
}

/* 覆盖Element UI表格样式 */
:deep(.el-table) {
  background-color: rgba(0, 0, 0, 0.2) !important;
  color: #fff !important;
  border-radius: 8px !important;
  height: 100% !important;
}

:deep(.el-table th) {
  background-color: rgba(65, 105, 225, 0.3) !important;
  color: #fff !important;
  border-bottom: 1px solid rgba(65, 105, 225, 0.5) !important;
  padding: 12px 0 !important;
  font-weight: bold !important;
}

:deep(.el-table tr) {
  background-color: transparent !important;
  transition: all 0.3s ease !important;
}

:deep(.el-table td) {
  border-bottom: 1px solid rgba(65, 105, 225, 0.2) !important;
  color: #fff !important;
  padding: 12px 0 !important;
}

:deep(.el-table--striped .el-table__body tr.el-table__row--striped td) {
  background-color: rgba(65, 105, 225, 0.15) !important;
}

:deep(.el-table__body tr.hover-row > td),
:deep(.el-table__body tr:hover > td) {
  background-color: rgba(65, 105, 225, 0.25) !important;
}

.delete-btn {
  background-color: #e53e3e !important;
}

/* 优化表格按钮样式 */
:deep(.el-button--small) {
  font-size: 13px !important;
  padding: 8px 15px !important;
  border-radius: 4px !important;
  margin: 0 5px !important;
}

:deep(.el-button--primary) {
  background-color: #409eff !important;
  border-color: #409eff !important;
}

:deep(.el-button--danger) {
  background-color: #f56c6c !important;
  border-color: #f56c6c !important;
}

:deep(.el-button--primary:hover),
:deep(.el-button--primary:focus) {
  background-color: #66b1ff !important;
  border-color: #66b1ff !important;
  transform: translateY(-1px);
  box-shadow: 0 2px 8px rgba(64, 158, 255, 0.5) !important;
}

:deep(.el-button--danger:hover),
:deep(.el-button--danger:focus) {
  background-color: #f78989 !important;
  border-color: #f78989 !important;
  transform: translateY(-1px);
  box-shadow: 0 2px 8px rgba(245, 108, 108, 0.5) !important;
}

:deep(.el-button [class*="el-icon-"] + span) {
  margin-left: 5px !important;
}

/* 回到顶部按钮样式 */
.scroll-to-top-btn {
  position: fixed;
  bottom: 20px;
  right: 20px;
  width: 50px; /* 增大按钮尺寸 */
  height: 50px; /* 增大按钮尺寸 */
  background-color: rgba(65, 105, 225, 0.8);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.3s ease;
  opacity: 0;
  pointer-events: none;
  z-index: 1000; /* 确保按钮在最上层 */
}

/* 顶部操作区域样式 */
.top-actions-container {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 10px 20px 15px; /* 增大内边距 */
  width: 100%;
  margin-bottom: 10px;
}

.left-actions {
  display: flex;
  align-items: center;
}

.right-actions {
  display: flex;
  align-items: center;
}

.new-schedule-btn {
  background-color: #3f68cd !important;
  border-color: #3f68cd !important;
  border-radius: 20px !important;
  padding: 12px 25px !important; /* 增大按钮内边距 */
  font-size: 14px !important;
  height: auto !important;
  min-height: 44px; /* 确保触摸区域足够大 */
  box-shadow: 0 2px 6px rgba(63, 104, 205, 0.5) !important;
  transition: all 0.3s ease;
  margin-right: 10px; /* 增大按钮间距 */
}

.new-schedule-btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 6px 14px rgba(63, 104, 205, 0.6) !important;
}

.scroll-to-top-btn.visible {
  opacity: 1;
  pointer-events: auto;
}

.scroll-to-top-btn:hover {
  background-color: rgba(65, 105, 225, 1);
}

.scroll-to-top-btn i {
  color: #fff;
  font-size: 18px;
}

/* 加载更多指示器样式 */
.loading-indicator {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 10px;
  color: #fff;
  font-size: 14px;
}

.loading-indicator i {
  margin-right: 8px;
}

/* 全部加载完毕提示样式 */
.load-all-indicator {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 10px;
  color: #fff;
  font-size: 14px;
}

/* 没有数据提示样式 */
.no-data-indicator {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 10px;
  color: #fff;
  font-size: 14px;
}

.no-data-indicator i {
  margin-right: 8px;
}

/* 分页信息样式 */
.pagination-info {
  display: flex;
  align-items: center;
  background-color: rgba(65, 105, 225, 0.2);
  border-radius: 20px;
  padding: 6px 15px;
  color: #fff;
  font-size: 14px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);
}

.pagination-info .divider {
  margin: 0 8px;
  color: rgba(255, 255, 255, 0.5);
}

/* 美化 el-checkbox 样式 */
:deep(.card-checkbox .el-checkbox__input) {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  position: relative;
  width: 24px; /* 增大复选框尺寸 */
  height: 24px; /* 增大复选框尺寸 */
}

:deep(.card-checkbox .el-checkbox__inner) {
  border-radius: 4px;
  width: 22px; /* 增大复选框尺寸 */
  height: 22px; /* 增大复选框尺寸 */
  background-color: rgba(34,44,74,0.7);
  border: 2px solid #3f68cd;
  position: relative;
  transition: all 0.2s;
}

:deep(.card-checkbox .el-checkbox__inner:hover) {
  border-color: #409eff;
}

:deep(.card-checkbox .el-checkbox__input.is-checked .el-checkbox__inner) {
  background-color: #409eff;
  border-color: #409eff;
}

:deep(.card-checkbox .el-checkbox__inner::after) {
  box-sizing: content-box;
  content: "";
  border: 2px solid #fff;
  border-left: 0;
  border-top: 0;
  height: 8px;
  left: 5px;
  position: absolute;
  top: 2px;
  transform: rotate(45deg) scaleY(0);
  width: 4px;
  transition: transform .15s ease-in .05s;
  transform-origin: center;
  opacity: 0;
}

:deep(.card-checkbox .el-checkbox__input.is-checked .el-checkbox__inner::after) {
  transform: rotate(45deg) scaleY(1);
  opacity: 1;
}

:deep(.card-checkbox .el-checkbox__label) {
  color: #fff;
  font-size: 15px;
  font-weight: 500;
  padding-left: 8px;
  display: none; /* 隐藏标签文本 */
}

:deep(.card-checkbox .el-checkbox__input.is-checked + .el-checkbox__label) {
  color: #409eff;
}
</style> 