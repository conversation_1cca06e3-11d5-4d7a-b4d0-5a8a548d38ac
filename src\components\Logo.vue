<template>
  <div class="logo-container" :class="{ 'clickable': clickable }">
    <img 
      :src="logoSrc" 
      :alt="alt" 
      class="logo-image" 
      :style="{ width: width, height: height }"
      @click="handleClick"
    />
  </div>
</template>

<script>
export default {
  name: 'AppLogo',
  props: {
    // logo图片路径，默认使用assets中的logo
    src: {
      type: String,
      default: require('@/assets/logo.png')
    },
    // logo替代文本
    alt: {
      type: String,
      default: '证件柜系统'
    },
    // logo宽度
    width: {
      type: String,
      default: '40px'
    },
    // logo高度
    height: {
      type: String,
      default: 'auto'
    },
    // 是否可点击
    clickable: {
      type: Boolean,
      default: false
    },
    // 点击后跳转的路由路径
    to: {
      type: String,
      default: '/'
    }
  },
  computed: {
    logoSrc() {
      return this.src;
    }
  },
  methods: {
    handleClick() {
      if (this.clickable) {
        // 如果设置了可点击，则导航到指定路由
        this.$router.push(this.to);
        this.$emit('click');
      }
    }
  }
}
</script>

<style scoped>
.logo-container {
  display: inline-flex;
  justify-content: center;
  align-items: center;
  padding: 5px;
  border-radius: 8px;
  overflow: hidden;
}

.logo-image {
  object-fit: contain;
  transition: transform 0.3s ease;
  border-radius: 8px;
}

.clickable {
  cursor: pointer;
}

.clickable .logo-image:hover {
  transform: scale(1.05);
}
</style> 