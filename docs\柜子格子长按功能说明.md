# 柜子格子长按修改状态功能说明

## 功能概述

在系统设置页面的"管理员开柜"选项卡中，柜子格子布局现在支持长按操作来修改格子状态。

## 功能特性

### 1. 长按触发
- **鼠标操作**: 在格子上按住鼠标左键1秒钟
- **触摸操作**: 在格子上长按1秒钟
- **视觉反馈**: 长按时会显示圆形进度指示器

### 2. 状态修改
- 长按完成后会弹出状态修改对话框
- 可以选择以下状态：
  - 空置中 (绿色)
  - 占用中 (红色) 
  - 开启中 (黄色)

### 3. 交互体验
- 长按过程中格子会放大显示
- 圆形进度条显示长按进度
- 支持中途取消长按操作

## 使用方法

### 步骤1: 进入管理员开柜页面
1. 登录管理员账号
2. 选择"管理员开柜"选项卡

### 步骤2: 长按格子
1. 在要修改的格子上长按（鼠标或触摸）
2. 观察圆形进度指示器
3. 保持按压直到进度完成

### 步骤3: 修改状态
1. 长按完成后会弹出状态修改对话框
2. 查看当前格子信息和状态
3. 选择新的状态
4. 点击"确认修改"按钮

## 技术实现

### 组件结构
```
src/views/sysSetting/index.vue - 主页面
src/components/BoxStatusDialog.vue - 状态修改对话框
```

### 关键功能
- **长按检测**: 使用定时器检测1秒长按
- **进度显示**: SVG圆形进度条
- **状态管理**: WebSocket通信更新状态
- **防误触**: 长按期间禁用点击事件

### 事件处理
- `@mousedown` / `@touchstart` - 开始长按
- `@mouseup` / `@touchend` - 结束长按
- `@mouseleave` / `@touchcancel` - 取消长按

## 样式特性

### 视觉效果
- 长按时格子放大1.1倍
- 圆形进度指示器带动画
- 不同状态对应不同颜色
- 科技蓝主题风格

### 响应式设计
- 支持鼠标和触摸操作
- 适配不同屏幕尺寸
- 流畅的动画过渡

## 注意事项

1. **权限要求**: 需要管理员登录权限
2. **网络连接**: 需要WebSocket连接正常
3. **操作确认**: 状态修改会立即生效
4. **防误操作**: 长按1秒才能触发，避免误操作

## 扩展功能

### 可能的增强
- 添加更多状态选项
- 支持批量状态修改
- 添加操作日志记录
- 支持撤销操作

### 自定义配置
- 长按时间可配置（当前1秒）
- 进度指示器样式可定制
- 状态颜色可自定义

## 故障排除

### 常见问题
1. **长按无响应**: 检查是否已登录管理员账号
2. **状态未更新**: 检查WebSocket连接状态
3. **触摸不灵敏**: 确保触摸时间足够长

### 调试信息
- 浏览器控制台会显示状态更新请求
- WebSocket消息类型: 10036 (updateBoxStatus)
