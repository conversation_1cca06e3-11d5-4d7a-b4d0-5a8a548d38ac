// 在安装时覆盖默认安装路径
const fs = require('fs');
const path = require('path');

// 强制安装到D:\icbBox
const targetDir = 'D:\\icbBox';

// 确保目标目录存在
try {
  if (!fs.existsSync(targetDir)) {
    fs.mkdirSync(targetDir, { recursive: true });
  }
  
  // 写入一个标记文件，表示这是正确的安装路径
  fs.writeFileSync(path.join(targetDir, '.install-path'), 'This directory is used by the 证件柜 application.');
  
  console.log(`Successfully prepared installation directory: ${targetDir}`);
} catch (error) {
  console.error(`Failed to prepare installation directory: ${error.message}`);
} 