<!-- 网络状态栏组件 -->
<template>
  <div v-if="shouldShowStatusBar" class="network-status-bar flex items-center justify-end">
    <!-- 网络状态 -->
    <div v-if="netLine !== ''" class="network-indicator flex items-center mr-2">
      <span class="network-dot" :class="[netLine == 1 ? 'connected' : 'disconnected']"></span>
      <span class="network-text ml-1" :class="[netLine == 1 ? 'text-green-400' : 'text-red-400']">
        {{ netLine == 1 ? '网络已连接' : '网络已断开' }}
      </span>
    </div>
    <!-- 显示在线/离线状态 -->

    <span v-if="isOnlineStatus === 0 || isOnlineStatus === 1" :class="[isOnlineStatus === 1 ? 'text-green-400' : 'text-red-400']">
      {{ isOnlineStatus === 1 ? '在线模式' : '离线模式' }}
    </span>
  </div>
</template>

<script>
export default {
  name: "NetworkStatusBar",
  inject: ["websocketService", "websocketMsg", "wsStatus"],
  props: {
    // 从父组件接收离线状态设置
    offline: {
      type: Number,
      default: 1 // 默认在线模式
    }
  },
  watch: {
    websocketMsg: {
      handler(newMessage) {
        console.log("收到新的状态组员消息:", newMessage);
        if (newMessage) {
          this.handleStatusMessages(newMessage);
        }
      },
      deep: true,
      immediate: true,
    },
    // 监听 offline prop 变化
    offline: {
      handler(newValue) {
        console.log("离线状态设置已更新:", newValue);
        this.isOnlineStatus = parseInt(newValue);
      },
      immediate: true
    }
  },
  data() {
    return {
      // 在线状态
      isOnlineStatus: this.offline,
      netLine: ''
    };
  },
  computed: {
    // 检查当前路由是否应该显示状态栏
    shouldShowStatusBar() {
      // 从路由元数据中获取显示状态栏的设置
      // 如果路由没有设置hideNetworkStatus或设置为false，则显示状态栏
      return !(this.$route.meta && this.$route.meta.hideNetworkStatus === true);
    }
  },
  mounted() {
    // 初始化网络状态
    this.loadLine();
    console.log("NetworkStatusBar 组件已挂载，当前离线状态:", this.isOnlineStatus);
  },
  
  beforeDestroy() {
    // 清理事件监听器
    window.removeEventListener('online', this.handleOnline);
    window.removeEventListener('offline', this.handleOffline);
  },
  
  methods: {
    handleStatusMessages(message) {
      if (!message) return;
      // 根据消息类型处理不同的情况
      if (message && message.messages) {
        console.log("系统设置消息:", message, message.messages.type);
        switch (message.messages.type) {
          case 20032: // 处理网络状态响应
            // 假设后端返回的网络状态为 1(已连接) 或 0(未连接)
            if (message.messages.data && typeof message.messages.data.status !== 'undefined') {
              this.netLine = message.messages.data.status;
              console.log("更新网络状态:", this.netLine);
            }
            break;
        }
      }
    },

    loadLine() {
      this.netLine = navigator.onLine ? 1 : 0;

      // 添加网络状态变化监听器
      window.addEventListener('online', this.handleOnline);
      window.addEventListener('offline', this.handleOffline);
    },
   
    // 处理在线状态变化
    handleOnline() {
      this.netLine = 1;
    },
    
    // 处理离线状态变化
    handleOffline() {
      this.netLine = 0;
    },
  }
};
</script>

<style scoped>
.network-status-bar {
  background: rgba(28, 31, 55, 0.7);
  border-radius: 4px;
  padding: 8px 12px;
  position: fixed;
  bottom: 50px;
  right: 20px;
  z-index: 2;
  box-shadow: 0 0 10px rgba(0, 0, 0, 0.3);
  font-size: 14px;
  max-width: calc(100vw - 40px);
}

/* 网络状态指示器样式 */
.network-dot {
  display: inline-block;
  width: 8px;
  height: 8px;
  border-radius: 50%;
}

.network-dot.connected {
  background-color: #10B981; /* 绿色 */
  box-shadow: 0 0 6px 1px rgba(16, 185, 129, 0.7);
}

.network-dot.disconnected {
  background-color: #EF4444; /* 红色 */
  box-shadow: 0 0 6px 1px rgba(239, 68, 68, 0.7);
}

.network-indicator {
  transition: all 0.3s ease;
}

/* 自适应不同屏幕分辨率 */
@media screen and (min-width: 1920px) {
  .network-status-bar {
    padding: 10px 15px;
    font-size: 16px;
    bottom: 70px;
    right: 30px;
  }
  
  .network-dot {
    width: 10px;
    height: 10px;
  }
}

@media screen and (max-width: 1600px) {
  .network-status-bar {
    padding: 9px 13px;
    font-size: 15px;
    bottom: 45px;
    right: 20px;
  }
}

@media screen and (max-width: 1366px) {
  .network-status-bar {
    padding: 7px 10px;
    font-size: 13px;
    bottom: 45px;
    right: 15px;
  }
}

@media screen and (max-width: 1024px) {
  .network-status-bar {
    padding: 6px 8px;
    font-size: 12px;
    bottom: 38px;
    right: 10px;
  }
  
  .network-dot {
    width: 7px;
    height: 7px;
  }
}

@media screen and (max-width: 768px) {
  .network-status-bar {
    padding: 5px 7px;
    font-size: 11px;
    bottom: 35px;
    right: 10px;
  }
  
  .network-dot {
    width: 6px;
    height: 6px;
  }
}
</style> 