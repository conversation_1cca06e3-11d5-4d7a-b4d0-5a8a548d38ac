<!-- 班表添加/编辑对话框组件 -->
<template>
  <el-dialog :title="dialogTitle" :visible.sync="dialogVisibleValue" width="40%" custom-class="group-dialog" @close="handleClose">
    <SoftKeyboard :autoInitialize="false"></SoftKeyboard>
    <el-form :model="form" :rules="rules" ref="form" label-width="100px">

     
      <el-form-item label="班表名称" prop="scheduleName">
          <el-input v-model.trim="form.scheduleName" placeholder="请输入"></el-input>
        </el-form-item>
        <el-form-item label="公司" prop="companyId">
          <el-cascader
            v-model="form.companyId"
            :options="organizationtreedata"
            :props="{
              value: 'id',
              label: 'organizationName',
              children: 'children',
              checkStrictly: true,
              emitPath: false,
            }"
            clearable
            style="width: 100%"
            size="small"
            placeholder="请选择公司"
            @change="handleCompanyChange"
          >
          </el-cascader>
        </el-form-item>
      
        <el-form-item label="工作部门" prop="workDeptId">
          <el-cascader
            v-model="form.workDeptId"
            :options="organizationtreedata"
            :props="{
              value: 'id', 
              label: 'organizationName',
              children: 'children',
              checkStrictly: true,
              emitPath: false
            }"
            clearable
            style="width: 100%"
            size="small"
            placeholder="请选择工作部门"
            @change="handleWorkDeptChange"
          >
          </el-cascader>
        </el-form-item>
    </el-form>
    <div slot="footer" class="dialog-footer">
      <el-button @click="handleCancel">取 消</el-button>
      <el-button type="primary" @click="submitForm">确 定</el-button>
    </div>
  </el-dialog>
</template>

<script>
import SoftKeyboard from "@/components/SoftKeyboard.vue";
export default {
  name: 'GroupDialog',
  components:{
    SoftKeyboard
  },
  props: {
    // 对话框标题
    dialogTitle: {
      type: String,
      default: '班表信息'
    },
    // 对话框是否可见
    dialogVisible: {
      type: Boolean,
      default: false
    },
    organizationtreedata:{
      type: Array,
      default: () => []
    },
    // 班表表单数据
    groupData: {
      type: Object,
      default: () => ({
        scheduleName: '',
        companyId: '',
        companyName: '',
        workDeptId: '',
        workDeptName: ''
      })
    }
  },
  data() {
    return {
      // 表单数据
      form: {
        scheduleName: '',
        companyId: '',
        companyName: '',
        workDeptId: '',
        workDeptName: ''
      },
      // 表单验证规则
      rules: {
        scheduleName: [
          { required: true, message: '请输入班表名称', trigger: 'blur' }
        ],
        companyId: [
          { required: true, message: '请选择公司', trigger: 'change' }
        ],
        workDeptId: [
          { required: true, message: '请选择工作部门', trigger: 'change' }
        ]
      }
    };
  },
  computed: {
    // 计算属性处理dialogVisible，避免直接修改prop
    dialogVisibleValue: {
      get() {
        return this.dialogVisible;
      },
      set(value) {
        this.$emit('update:dialogVisible', value);
      }
    }
  },
  watch: {
    // 监听传入的班表数据变化
    groupData: {
      handler(newVal) {
        if (newVal) {
          this.form = JSON.parse(JSON.stringify(newVal));
        }
      },
      immediate: true,
      deep: true
    }
  },
  methods: {
    // 处理公司选择变化
    handleCompanyChange(val) {
      if (val) {
        // 递归查找节点
        const findNode = (nodes, id) => {
          for (let node of nodes) {
            if (node.id === id) {
              return node;
            }
            if (node.children && node.children.length > 0) {
              const found = findNode(node.children, id);
              if (found) return found;
            }
          }
          return null;
        };
        
        const node = findNode(this.organizationtreedata, val);
        if (node) {
          this.form.companyId = node.id;
          this.form.companyName = node.organizationName;
        }
      } else {
        this.form.companyId = '';
        this.form.companyName = '';
      }
    },
    
    // 处理工作部门选择变化
    handleWorkDeptChange(val) {
      if (val) {
        // 递归查找节点
        const findNode = (nodes, id) => {
          for (let node of nodes) {
            if (node.id === id) {
              return node;
            }
            if (node.children && node.children.length > 0) {
              const found = findNode(node.children, id);
              if (found) return found;
            }
          }
          return null;
        };
        
        const node = findNode(this.organizationtreedata, val);
        if (node) {
          this.form.workDeptId = node.id;
          this.form.workDeptName = node.organizationName;
        }
      } else {
        this.form.workDeptId = '';
        this.form.workDeptName = '';
      }
    },

    // 提交表单
    submitForm() {
      this.$refs.form.validate(valid => {
        if (valid) {
          this.$emit('submit', this.form);
        }
      });
    },
    // 取消操作
    handleCancel() {
      this.$emit('update:dialogVisible', false);
    },
    // 关闭对话框时
    handleClose() {
      this.$refs.form.resetFields();
      this.$emit('update:dialogVisible', false);
    }
  },
  created() {
    // this.getOrganizationData();
  }
};
</script>

<style scoped>
/* 对话框样式 */
:deep(.el-dialog) {
  background-color: #1C1F37 !important;
  border: 1px solid rgba(65, 105, 225, 0.5) !important;
  border-radius: 12px !important;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.5) !important;
  overflow: hidden !important;
}

:deep(.el-dialog__header) {
  background-color: rgba(65, 105, 225, 0.2) !important;
  padding: 15px 20px !important;
  border-bottom: 1px solid rgba(65, 105, 225, 0.3) !important;
}

:deep(.el-dialog__title) {
  color: #fff !important;
  font-weight: bold !important;
}

:deep(.el-dialog__body) {
  color: #fff !important;
  padding: 20px !important;
}

:deep(.el-dialog__footer) {
  border-top: 1px solid rgba(65, 105, 225, 0.3) !important;
  padding: 15px 20px !important;
}

:deep(.el-form-item__label) {
  color: #fff !important;
}

:deep(.el-input__inner), :deep(.el-select__input) {
  background-color: rgba(0, 0, 0, 0.3) !important;
  border-color: rgba(65, 105, 225, 0.5) !important;
  color: #fff !important;
  transition: all 0.3s ease !important;
}

:deep(.el-input__inner:focus), :deep(.el-select__input:focus) {
  border-color: rgba(65, 105, 225, 0.8) !important;
  box-shadow: 0 0 10px rgba(65, 105, 225, 0.3) !important;
}

:deep(.el-button) {
  border-radius: 4px !important;
  transition: all 0.3s ease !important;
}

:deep(.el-button--primary) {
  background-color: rgba(65, 105, 225, 0.8) !important;
  border-color: rgba(65, 105, 225, 0.8) !important;
  box-shadow: 0 4px 10px rgba(65, 105, 225, 0.3) !important;
}

:deep(.el-button--primary:hover) {
  background-color: rgba(65, 105, 225, 1) !important;
  transform: translateY(-2px);
  box-shadow: 0 6px 14px rgba(65, 105, 225, 0.4) !important;
}

/* 下拉菜单样式 */
:deep(.el-select-dropdown) {
  background-color: #1C1F37 !important;
  border: 1px solid rgba(65, 105, 225, 0.5) !important;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.3) !important;
}

:deep(.el-select-dropdown__item) {
  color: #fff !important;
}

:deep(.el-select-dropdown__item.hover), :deep(.el-select-dropdown__item:hover) {
  background-color: rgba(65, 105, 225, 0.3) !important;
}

:deep(.el-select-dropdown__item.selected) {
  background-color: rgba(65, 105, 225, 0.5) !important;
  color: #fff !important;
}
</style> 