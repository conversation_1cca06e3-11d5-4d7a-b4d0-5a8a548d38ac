const fs = require('fs');
const path = require('path');

// 获取package.json路径
const pkgPath = path.resolve(__dirname, '../../package.json');
const pkg = require(pkgPath);

// 解析版本号
let [major, minor, patch] = pkg.version.split('.').map(Number);
patch += 1;
const newVersion = [major, minor, patch].join('.');
pkg.version = newVersion;

// 写回package.json
fs.writeFileSync(pkgPath, JSON.stringify(pkg, null, 2), 'utf-8');
console.log('版本号已自增为：', newVersion); 