/**
 * 准备Linux打包所需的不同尺寸图标
 * 此脚本可以帮助从source-icon.png生成Linux所需的不同尺寸图标
 * 需要安装sharp依赖: npm install sharp --save-dev
 */

const fs = require('fs');
const path = require('path');
const sharp = require('sharp');

// 需要生成的图标尺寸
const sizes = [16, 24, 32, 48, 64, 128, 256, 512];
const sourceIcon = path.join(__dirname, 'source-icon.png');
const iconDir = path.join(__dirname, 'build', 'icons');

// 确保图标目录存在
if (!fs.existsSync(iconDir)) {
  fs.mkdirSync(iconDir, { recursive: true });
}

// 为每个尺寸生成图标
async function generateIcons() {
  try {
    // 确保源图标存在
    if (!fs.existsSync(sourceIcon)) {
      console.error('错误: 源图标文件不存在:', sourceIcon);
      return;
    }

    console.log('开始生成Linux图标...');
    
    for (const size of sizes) {
      const iconPath = path.join(iconDir, `${size}x${size}.png`);
      
      await sharp(sourceIcon)
        .resize(size, size)
        .toFile(iconPath);
      
      console.log(`✅ 已生成 ${size}x${size} 图标`);
    }
    
    // 复制一份作为默认icon.png (如果尚未存在)
    if (!fs.existsSync(path.join(iconDir, 'icon.png'))) {
      fs.copyFileSync(sourceIcon, path.join(iconDir, 'icon.png'));
      console.log('✅ 已复制默认icon.png');
    }
    
    console.log('所有Linux图标生成完成！');
    console.log('现在可以使用 npm run electron:build:deb 命令构建DEB包');
  } catch (error) {
    console.error('生成图标时出错:', error);
  }
}

generateIcons(); 