<!-- 退出登录确认对话框组件 -->
<template>
  <div v-if="visible" class="fixed inset-0 flex items-center justify-center z-50">
    <!-- 背景遮罩 -->
    <div class="absolute inset-0 bg-black bg-opacity-60" @click="onCancel"></div>
    
    <!-- 对话框 -->
    <div class="confirm-dialog bg-gray-800 rounded-lg p-6 w-80 shadow-2xl transform transition-all scale-in">
      <div class="text-center mb-6">
        <div class="text-4xl text-red-400 mb-4">
          <i class="fas fa-sign-out-alt"></i>
        </div>
        <h3 class="text-xl font-bold text-white">{{ title }}</h3>
        <p class="text-gray-300 mt-2">{{ message }}</p>
      </div>
      
      <div class="flex justify-center space-x-4">
        <button 
          @click="onCancel"
          class="px-4 py-2 bg-gray-600 text-white rounded hover:bg-gray-500 transition-colors"
        >
          {{ cancelText }}
        </button>
        <button 
          @click="onConfirm"
          class="px-4 py-2 bg-blue-600 text-white rounded hover:bg-blue-500 transition-colors"
        >
          {{ confirmText }}
        </button>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'LogoutConfirmDialog',
  props: {
    // 控制对话框是否可见
    visible: {
      type: Boolean,
      default: false
    },
    // 对话框标题
    title: {
      type: String,
      default: '确认退出登录'
    },
    // 对话框消息内容
    message: {
      type: String,
      default: '您确定要退出当前登录吗？'
    },
    // 取消按钮文本
    cancelText: {
      type: String,
      default: '取消'
    },
    // 确认按钮文本
    confirmText: {
      type: String,
      default: '确认退出'
    }
  },
  methods: {
    // 取消操作
    onCancel() {
      this.$emit('cancel');
    },
    // 确认操作
    onConfirm() {
      this.$emit('confirm');
    }
  }
};
</script>

<style scoped>
.scale-in {
  animation: scaleIn 0.2s ease-out;
}

@keyframes scaleIn {
  from {
    transform: scale(0.9);
    opacity: 0;
  }
  to {
    transform: scale(1);
    opacity: 1;
  }
}
</style> 