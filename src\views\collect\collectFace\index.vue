 <!-- 代码已包含 CSS：使用 TailwindCSS , 安装 TailwindCSS 后方可看到布局样式效果 -->
<template>
  <div
    class="min-h-screen h-screen bg-gray-900 tech-grid flex items-center justify-center"
    style="overflow: hidden"
  >
    <Logo class="absolute top-8 left-8 z-40" />

    <UserInfoBar class="absolute bottom-8 right-8 z-40" />
    <!-- 返回按钮 -->

    <div class="absolute top-8 right-8 z-50 flex gap-4">
      <!-- 使用LogoutButton组件替代原来的返回登录按钮 -->
      <BackButton />
    </div>

    <div class="w-[1440px] min-h-[1024px] relative">
      <!-- 主要内容区 -->
      <div
        class="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2"
      >
        <!-- 识别框容器 -->
        <div class="relative w-[500px] h-[500px]">
          <!-- 扫描动画 -->
          <div class="absolute inset-0 border-2 border-blue-400/50">
            <!-- <div
              class="absolute top-0 left-0 w-full h-1 bg-gradient-to-r from-transparent via-blue-400 to-transparent transform animate-scan"
            ></div> -->
          </div>
          <!-- L型装饰 -->
          <div class="absolute top-0 left-0 w-16 h-2 bg-blue-400"></div>
          <div class="absolute top-0 left-0 w-2 h-16 bg-blue-400"></div>
          <div class="absolute top-0 right-0 w-16 h-2 bg-blue-400"></div>
          <div class="absolute top-0 right-0 w-2 h-16 bg-blue-400"></div>
          <div class="absolute bottom-0 left-0 w-16 h-2 bg-blue-400"></div>
          <div class="absolute bottom-0 left-0 w-2 h-16 bg-blue-400"></div>
          <div class="absolute bottom-0 right-0 w-16 h-2 bg-blue-400"></div>
          <div class="absolute bottom-0 right-0 w-2 h-16 bg-blue-400"></div>
          <!-- 中心识别区域 -->
          <!--  -->
          <div
            v-show="!isCameraActive && !showCapturedImage"
            class="absolute inset-8 border border-gray-600 bg-gray-800/50 flex items-center justify-center"
          >
            <div class="text-center">
              <i class="fas fa-user-circle text-6xl text-blue-400 mb-4"></i>
              <p class="text-gray-300 text-lg">{{ statusMessage }}</p>
            </div>
          </div>
          <div v-show="false">放采集图片</div>

          <div
            class="w-full h-full"
            v-show="isCameraActive && !showCapturedImage"
          >
            <video
              ref="video"
                :style="{ transform: `rotate(${rotationDegree}deg)` }"
              class="w-full h-full object-cover"
              autoplay
            ></video>



                <!-- 添加旋转控制按钮 -->
    <div class="absolute bottom-4 left-4 flex gap-2">
      <button 
        @click="rotateCamera(-90)" 
        class="bg-blue-500 text-white p-2 rounded-full"
        title="逆时针旋转"
      >
        <i class="fas fa-undo"></i>
      </button>
      <button 
        @click="rotateCamera(90)" 
        class="bg-blue-500 text-white p-2 rounded-full"
        title="顺时针旋转"
      >
        <i class="fas fa-redo"></i>
      </button>
    </div>


          </div>

          <!-- 显示拍摄的照片 -->
          <div class="w-full h-full" v-show="showCapturedImage">
            <img :src="capturedImage" class="w-full h-full object-cover" />
          </div>
        </div>

        <!-- 开始拍照按钮 -->
        <div class="flex justify-center mt-6">
          <button
            v-if="!isCameraActive && !showCapturedImage"
            @click="startTakePhoto"
            class="px-6 py-3 bg-blue-500 hover:bg-blue-600 text-white font-medium rounded-lg flex items-center transition-all duration-200 transform hover:scale-105"
          >
            <i class="fas fa-camera mr-2"></i>
            开始拍照
          </button>

          <button
            v-if="isCameraActive && !showCapturedImage"
            @click="takePhoto"
            class="px-6 py-3 bg-blue-500 hover:bg-blue-600 text-white font-medium rounded-lg flex items-center transition-all duration-200 transform hover:scale-105"
          >
            <i class="fas fa-camera mr-2"></i>
            拍照
          </button>

          <!-- 重新拍照和确认按钮 -->
          <div v-if="showCapturedImage" class="flex gap-4">
            <button
              @click="retakePhoto"
              class="px-6 py-3 bg-gray-500 hover:bg-gray-600 text-white font-medium rounded-lg flex items-center transition-all duration-200 transform hover:scale-105"
            >
              <i class="fas fa-redo mr-2"></i>
              重新拍照
            </button>
            <button
              @click="confirmPhoto"
              class="px-6 py-3 bg-green-500 hover:bg-green-600 text-white font-medium rounded-lg flex items-center transition-all duration-200 transform hover:scale-105"
            >
              <i class="fas fa-check mr-2"></i>
              确认
            </button>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>
  <script>
import UserInfoBar from "@/components/UserInfoBar.vue";
import storage from "@/utils/storage";
import BackButton from "@/components/BackButton.vue";
import Logo from "@/components/Logo.vue";
export default {
  name: "Face-Collect",
  components: {
    UserInfoBar,
    BackButton,
    Logo,
  },
  data() {
    return {
      rotationDegree: 0, // 添加旋转角度状态
      isProcessing: false,
      isGoHome: false,
      faceParams: {},
      videoDevices: [],
      selectedDeviceId: "",
      stream: null,
      statusMessage: "请将人脸对准摄像头",
      isCameraActive: false,
      capturedImage: null, // 添加存储拍摄照片的数据
      showCapturedImage: false, // 控制显示拍摄的照片
    };
  },
  inject: ["speak", "websocketService", "websocketMsg", "speakSync"],
  watch: {
    websocketMsg: {
      handler(newMessage) {
        console.log("收到新的WebSocket消息face:", newMessage);
        if (newMessage) {
          this.handleFaceRecognitionMessages(newMessage);
        }
      },
      deep: true,
      immediate: true,
    },
  },
  created() {
    // 获取路由参数中的loginType
    this.getConent();
  },
  mounted() {
    this.getVideoDevices(); // 组件加载时获取摄像头列表
  },
  destroyed() {
    this.closeCamera();
  },
  methods: {
    rotateCamera(degree) {
      this.rotationDegree = (this.rotationDegree + degree) % 360;
    },
    // 开始拍照按钮点击事件
    startTakePhoto() {
      if (!this.isCameraActive) {
        // 如果摄像头未激活，则开始摄像头
        // this.sendOrder();
        this.getconfig();
        // this.speak("正在启动人脸识别");
      } else {
        // 如果摄像头已激活，可以提示用户已开启
        this.speak("人脸识别已开启，请将面部对准摄像头");
      }
    },
    getconfig() {
      //请求人脸识别请求
      const order = {
        type: 10031,
        id: "getconfig",
        data: {},
      };
      this.websocketService.send(order);
    },
    endFace() {
      //请求人脸识别结束
      const order = {
        type: 10023,
        id: "endFace",
        data: {},
      };
      this.websocketService.send(order);
    },

    async getVideoDevices() {
      try {
        const devices = await navigator.mediaDevices.enumerateDevices();

        this.videoDevices = devices.filter(
          (device) => device.kind === "videoinput"
        );

        console.log("this.videoDevices", this.videoDevices);
      } catch (error) {
        console.error("获取摄像头列表失败:", error);
      }
    },
    async startCamera(selectedDeviceId) {
      try {
        console.log("startCamera", selectedDeviceId);
        this.statusMessage = "正在打开摄像头...";
        this.isProcessing = true;
        // console.log("selectedDeviceId",selectedDeviceId)
        this.speak("正在打开摄像头");
        // 添加语音提示
        // 重点：根据用户选择的摄像头ID打开
        this.stream = await navigator.mediaDevices.getUserMedia({
          video: {
            deviceId: selectedDeviceId
              ? { exact: selectedDeviceId }
              : undefined,
          },
          audio: false,
        });
        this.$nextTick(() => {
          if (this.$refs.video) this.$refs.video.srcObject = this.stream;
          this.isCameraActive = true;
          this.isProcessing = false;
          this.statusMessage = "摄像头已准备就绪";
          this.speakSync("摄像头已准备就绪请将人脸对准摄像头"); // 进入页面时播报提示语
        });
      } catch (error) {
        console.error("打开摄像头失败:", error);
        this.isProcessing = false;

        // 更详细的错误处理
        if (error.name === 'NotAllowedError' || error.name === 'PermissionDeniedError') {
          this.statusMessage = `摄像头权限被拒绝，请允许摄像头访问`;
          this.speak("摄像头权限被拒绝，请允许摄像头访问");
        } else if (error.name === 'NotFoundError' || error.name === 'DevicesNotFoundError') {
          this.statusMessage = `未找到摄像头设备，请检查摄像头连接`;
          this.speak("未找到摄像头设备，请检查摄像头连接");
        } else if (error.name === 'NotReadableError' || error.name === 'TrackStartError') {
          this.statusMessage = `摄像头被占用或无法启动，请关闭其他可能使用摄像头的应用`;
          this.speak("摄像头被占用或无法启动，请关闭其他可能使用摄像头的应用");
        } else {
          this.statusMessage = `摄像头初始化失败: ${error.message}`;
          this.speak("摄像头初始化失败，请检查摄像头权限");
        }
      }
    },

    closeCamera() {
      this.isCameraActive = false;
      if (this.stream) {
        // 获取所有轨道并逐个关闭
        this.stream.getTracks().forEach((track) => track.stop());
        this.stream = null;
        this.isCameraActive = false;
        this.statusMessage = "摄像头已关闭";
      }
      if (this.$refs.video) {
        this.$refs.video.srcObject = null;
      }
    },

    async getConent(){
      const currUser = await storage.local.get('currsorEditUser');
      let order = {
        type: 10020,
        id: "getConent",
        data: {
          userId: Number(currUser.id),
          type: 2,
        },
      };
      this.websocketService.send(order);
    },
    // 处理人脸识别相关的消息
    async handleFaceRecognitionMessages(message) {
      if (!message) return;

      // console.log("处理人脸识别消息:", message.messages);

      // 根据消息类型处理不同的情况
      if (message && message.messages) {
        switch (message.messages.type) {
          case 20031: // 请求人脸识别响应
            if (message.messages.data.code === 1) {
              if (message.messages.data) {
                var camerIndex = message.messages.data.camerIndex;
                const matchedDevice = this.videoDevices[camerIndex];
                if (matchedDevice) {
                  // 如果找到匹配的设备，使用该设备的ID启动摄像头
                  this.closeCamera();
                  this.startCamera(matchedDevice.deviceId);
                } else {
                  // 如果没有找到匹配的设备，使用默认设备
                  this.speak("未找到匹配的摄像头设备"); // 添加错误语音提示
                }
              }
            } else {
              this.speak(message.messages.data.msg);
            }
            break;
          case 20019: // 人脸照片上传响应
            if (message.messages.data.code === 1) {
              this.speak("照片上传成功");
              // 可以在这里添加照片上传成功后的处理逻辑
              setTimeout(() => {
                this.$router.go(-1);
              }, 1500);
            } else {
              this.speak(message.messages.data.msg || "照片上传失败");
              this.retakePhoto(); // 上传失败重新拍照
            }
            break;

          case 20020: // 采集成功
            if (message.messages.data.code === 1) {
              this.showCapturedImage = true;
              this.capturedImage = 'data:image/jpeg;base64,' + message.messages.data.content;
            } else {
              this.speak(message.messages.data.msg);
            }
            break;

          default:
            console.log("未处理的消息类型:", message);
        }
      }
    },

    // 拍照功能
    takePhoto() {
      if (!this.isCameraActive || !this.$refs.video) return;

      try {
        // 创建一个canvas元素来捕获视频帧
        const canvas = document.createElement("canvas");
        const video = this.$refs.video;
        const width = video.videoWidth;
        const height = video.videoHeight;

        // canvas.width = width;
        // canvas.height = height;

        // // 在canvas上绘制当前视频帧
        // const ctx = canvas.getContext("2d");
        // ctx.drawImage(video, 0, 0, width, height);

            // 根据旋转角度决定canvas的宽高
            const isVertical = this.rotationDegree % 180 !== 0;
        canvas.width = isVertical ? height : width;
        canvas.height = isVertical ? width : height;

        // 在canvas上绘制当前视频帧，考虑旋转角度
        const ctx = canvas.getContext("2d");
        ctx.save();
        ctx.translate(canvas.width / 2, canvas.height / 2);
        ctx.rotate((this.rotationDegree * Math.PI) / 180);
        ctx.drawImage(
          video, 
          -width / 2, 
          -height / 2, 
          width, 
          height
        );
        ctx.restore();

        // 将canvas内容转换为base64格式的图片数据
        this.capturedImage = canvas.toDataURL("image/jpeg");
        this.showCapturedImage = true;

        this.speak("已拍照，请确认照片");
      } catch (error) {
        console.error("拍照失败:", error);
        this.speak("拍照失败，请重试");
      }
    },

    // 重新拍照
    retakePhoto() {
      this.capturedImage = null;
      this.showCapturedImage = false;
      this.speak("请重新拍照");
    },

    // 确认照片并上传
    async confirmPhoto() {
      if (!this.capturedImage) return;

      this.speak("正在上传照片");

      // 移除base64前缀，只保留图片数据部分
      const base64Data = this.capturedImage.split(",")[1];

      console.log("base64Data", base64Data);
      const currUser = await storage.local.get("currsorEditUser");
      console.log("currUser", currUser);
      let order = {
        type: 10019, //
        id: "saveFace",
        data: {
          userId: Number(currUser.id),
          type: 2,
          content: base64Data,
        },
      };

      this.websocketService.send(order);
    },
  },
};
</script>
  <style scoped>
.animate-scan {
  animation: scan 2s linear infinite;
}
@keyframes scan {
  0% {
    transform: translateY(0);
  }
  100% {
    transform: translateY(500px);
  }
}
/* 自定义输入框样式 */
input::-webkit-outer-spin-button,
input::-webkit-inner-spin-button {
  -webkit-appearance: none;
  margin: 0;
}
.tech-grid {
  background-image: linear-gradient(rgba(0, 102, 255, 0.1) 1px, transparent 1px),
    linear-gradient(90deg, rgba(0, 102, 255, 0.1) 1px, transparent 1px);
  background-size: 20px 20px;
}
</style>
  