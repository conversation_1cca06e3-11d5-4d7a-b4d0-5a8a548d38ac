<!-- 班组成员管理对话框 -->
<template>
  <el-dialog
    title="管理组员"
    :visible.sync="dialogVisibleValue"
    width="60%"
    :fullscreen="true"
    custom-class="members-dialog"
  >
    <!-- 搜索和添加区域 -->
    <div class="top-actions-container">
      <div class="left-actions">
        <el-button
          type="primary"
          icon="el-icon-plus"
          @click="handleAddMember"
          class="add-btn"
          >添加成员</el-button
        >
      </div>
      <div class="right-actions">
        <!--分页信息-->
        <div class="pagination-info">
          <span>当前第 {{ currentPage }} 页</span>
          <span class="divider">|</span>
          <span>共 {{ Math.ceil(totalItems / pageSize) || 0 }} 页</span>
          <span class="divider">|</span>
          <span>总计 {{ totalItems }} 条</span>
        </div>
      </div>
    </div>

    <!-- 卡片列表容器 -->
    <div class="card-list-container" ref="cardListContainer" @scroll="handleScroll">
      <div class="card-grid">
        <div 
          v-for="member in members" 
          :key="member.id"
          class="member-card"
        >
          <div class="member-card-content">
            <div class="info-row">
              <div class="info-label">用户名：</div>
              <div class="info-value">{{ member.userName }}</div>
            </div>
            <div class="info-row">
              <div class="info-label">姓名：</div>
              <div class="info-value">{{ member.displayName }}</div>
            </div>
            <div class="info-row">
              <div class="info-label">手机号：</div>
              <div class="info-value">{{ member.mobile }}</div>
            </div>
            <div class="info-row">
              <div class="info-label">组员类型：</div>
              <div class="info-value">
                <el-tag :type="member.type === 'GROUP_LEADER' ? 'success' : 'info'">
                  {{ member.type === "GROUP_LEADER" ? "班组长" : "员工" }}
                </el-tag>
              </div>
            </div>
          </div>
          <div class="member-card-actions">
            <el-button
              size="mini"
              type="primary"
              @click="handleEditMember(member)"
              class="action-btn"
              >修改组员类型</el-button
            >
            <el-button
              size="mini"
              type="danger"
              @click="handleDeleteMember(member)"
              class="action-btn"
              >移除本组</el-button
            >
          </div>
        </div>
      </div>
      
      <!-- 加载更多指示器 -->
      <div v-if="isLoading" class="loading-indicator">
        <i class="el-icon-loading"></i>
        <span>加载中...</span>
      </div>
      
      <!-- 全部加载完毕提示 -->
      <div v-if="allDataLoaded && members.length > 0" class="load-all-indicator">
        <span>——— 已加载全部数据 ———</span>
      </div>
      
      <!-- 没有数据提示 -->
      <div v-if="!isLoading && members.length === 0" class="no-data-indicator">
        <i class="el-icon-warning-outline"></i>
        <span>暂无数据</span>
      </div>
    </div>

    <!-- 回到顶部按钮 -->
    <div 
      class="scroll-to-top-btn"
      :class="{ 'visible': showScrollTopBtn }"
      @click="scrollToTop"
    >
      <i class="el-icon-arrow-up"></i>
    </div>

    <!-- 添加/编辑成员对话框 -->
    <el-dialog
      :title="memberDialogTitle"
      :visible.sync="memberFormVisible"
      width="40%"
      append-to-body
      custom-class="member-form-dialog"
    >

    <SoftKeyboard :autoInitialize="false"></SoftKeyboard>
      <el-form
        :model="memberForm"
        :rules="memberRules"
        ref="memberForm"
        label-width="100px"
      >
        <!-- 编辑模式表单 -->
        <template v-if="isEditMode">
          <el-form-item label="用户名">
            <el-input v-model="memberForm.userName" disabled></el-input>
          </el-form-item>
          <el-form-item label="姓名">
            <el-input v-model="memberForm.displayName" disabled></el-input>
          </el-form-item>
          <el-form-item label="组员类型" prop="type">
            <el-select
              v-model="memberForm.type"
              placeholder="请选择组员类型"
              style="width: 100%"
            >
              <el-option label="班组长" value="GROUP_LEADER"></el-option>
              <el-option label="员工" value="EMPLOYEE"></el-option>
            </el-select>
          </el-form-item>
        </template>

        <!-- 新增模式表单 -->
        <template v-else>
          <el-form-item label="选择人员" prop="list">
            <el-select
            v-mobile-select
            filterable
              v-model="memberForm.list"
              multiple
              placeholder="请选择要添加的人员"
              style="width: 100%"
            >
              <el-option
                v-for="user in userOptions"
                :key="user.id"
               :label="`${user.account} ${user.realName} ${user.mobile}`"
                :value="user.id"
                :disabled="user.disabled"
              >
              </el-option>
            </el-select>
          </el-form-item>
       
        </template>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="memberFormVisible = false">取 消</el-button>
        <el-button type="primary" @click="submitMemberForm">确 定</el-button>
      </div>
    </el-dialog>

    <!-- 删除确认对话框 -->
    <el-dialog
      title="确认移除"
      :visible.sync="deleteDialogVisible"
      width="30%"
      append-to-body
      custom-class="delete-dialog"
    >
      <span
        >确定要将 "{{ deleteItem.name }}" 从班组中移除吗？此操作不可恢复。</span
      >
      <span slot="footer" class="dialog-footer">
        <el-button @click="deleteDialogVisible = false">取 消</el-button>
        <el-button type="danger" @click="confirmDelete">确 定</el-button>
      </span>
    </el-dialog>
  </el-dialog>
</template>

<script>
import SoftKeyboard from "@/components/SoftKeyboard.vue";

export default {
  name: "GroupMembersDialog",
  inject: ["speak", "websocketService", "websocketMsg", "speakSync"],
  components:{
    SoftKeyboard
  },
  watch: {
    websocketMsg: {
      handler(newMessage) {
        console.log("收到新的WebSocket组员消息:", newMessage);
        if (newMessage) {
          this.handleGroupPersonMessages(newMessage);
        }
      },
      deep: true,
      immediate: true,
    },
    // 添加对dialogVisible的监听
    dialogVisible: {
      handler(newVal) {
        if (newVal === true) {
          // 当弹窗打开时，调用getdata方法获取数据
          this.getdata();
        }
      },
      immediate: true,
    },
  },
  props: {
    // 对话框是否可见
    dialogVisible: {
      type: Boolean,
      default: false,
    },
    // 班组ID
    groupId: {
      type: [Number, String],
      default: null,
    },
    // 班组名称
    groupName: {
      type: String,
      default: "未知班组",
    },
  },
  data() {
    return {
      userOptions:[],
      memberOptions: [],
      searchText: "",
      currentPage: 1,
      pageSize: 20,
      totalItems: 0,
      memberDialogTitle: "",
      memberFormVisible: false,
      deleteDialogVisible: false,
      deleteItem: {},
      isEditMode: false, // 新增标记，用于区分是新增还是编辑模式
      memberForm: {
        id: null,
        username: "",
        name: "",
        workId: "",
        position: "",
        phone: "",
        isGroupLeader: false,
        createTime: null,
        list: [], // 添加选择的用户数组
      },
      memberRules: {
        type: [
          { required: true, message: "请选择组员类型", trigger: "change" },
        ],
        list: [
          { required: true, message: "请选择至少一名人员", trigger: "change" },
          { type: "array", min: 1, message: "至少选择一名人员", trigger: "change" }
        ]
      },
      // 班组成员列表数据
      members: [],
      availableUsers: [], // 可选用户列表
      isLoading: false,
      allDataLoaded: false,
      showScrollTopBtn: false,
      scrollThreshold: 100, // 距离底部多少像素开始加载
    };
  },
  computed: {
    // 计算属性处理dialogVisible，避免直接修改prop
    dialogVisibleValue: {
      get() {
        return this.dialogVisible;
      },
      set(value) {
        this.$emit("update:dialogVisible", value);
      },
    },
  },
  created() {
    // 初始化数据
    this.updateTotalItems();
  },
  mounted() {
    // 如果弹窗一开始就是显示状态，则获取数据
    if (this.dialogVisible) {
      this.getdata();
    }
    
    // 添加滚动事件监听
    this.$nextTick(() => {
      if (this.$refs.cardListContainer) {
        this.$refs.cardListContainer.addEventListener('scroll', this.toggleScrollTopButton);
      }
    });
  },
  beforeDestroy() {
    // 移除滚动事件监听
    if (this.$refs.cardListContainer) {
      this.$refs.cardListContainer.removeEventListener('scroll', this.toggleScrollTopButton);
    }
  },
  methods: {
    handleGroupPersonMessages(message) {
      if (!message) return;
      // 根据消息类型处理不同的情况
      if (message && message.messages) {
        switch (message.messages.type) {
          case 20050: //获取班组分页
            this.isLoading = false;
            if (message.messages.data && message.messages.data.code === 1) {
              const newData = message.messages.data.list || [];
              this.totalItems = message.messages.data.total || 0;
              
              if (this.currentPage === 1) {
                // 首次加载或刷新
                this.members = newData;
              } else if (newData.length > 0) {
                // 追加数据
                this.members = [...this.members, ...newData];
              }
              
              // 判断是否已加载所有数据
              this.allDataLoaded = this.members.length >= this.totalItems || newData.length === 0;
              this.updateTotalItems();
            } else {
              this.speak(message.messages.data.msg);
            }
            break;
          case 20052: // 删除班组成员
            if (message.messages.data && message.messages.data.code === 1) {
              this.resetAndReload();
              this.deleteDialogVisible = false;
            } else {
              this.speak(message.messages.data.msg || "删除班组成员失败");
            }
            break;
          case 20051: // 添加班组成员
            if (message.messages.data && message.messages.data.code === 1) {
              this.resetAndReload();
              this.memberFormVisible = false;
            } else {
              this.speak(message.messages.data.msg || "添加班组成员失败");
            }
            break;
          case 20054: // 修改班组成员信息
            if (message.messages.data && message.messages.data.code === 1) {
              this.resetAndReload();
              this.memberFormVisible = false;
            } else {
              this.speak(message.messages.data.msg || "修改班组成员信息失败");
            }
            break;
          case 20100: // 获取可用用户列表
            if (message.messages.data && message.messages.data.code === 1) {
              this.availableUsers = message.messages.data.list || [];
            } else {
              this.speak(message.messages.data.msg || "获取可用用户列表失败");
            }
            break;

            case 20201: //获取绑定的部门ID列表
            if (message.messages.data && message.messages.data.code === 1) {
              var orgs = message.messages.data.data;
               this.getUserFindList(orgs)
            } else {
              this.speak(message.messages.data.msg);
            }
            break;
            case 20202: //获取部门用户列表
            if (message.messages.data && message.messages.data.code === 1) {
              console.log("获取部门用户列表",message.messages.data.list);
               this.availableUsers = message.messages.data.list || [];
               this.getUserBindList()
            } else {
              this.speak(message.messages.data.msg);
            }
            break;
            case 20053: //获取已绑定数据
            if (message.messages.data && message.messages.data.code === 1) {         
                var boundUserIds = message.messages.data.list 
              console.log('boundUserIds',boundUserIds);
              console.log('availableUsers',this.availableUsers);
              this.userOptions = (this.availableUsers || []).map((user) => ({
              ...user,
              disabled: boundUserIds.includes(+user.id),
            }));

            } else {
              this.speak(message.messages.data.msg);
            }
            break;   
            
            
          default:
          // console.log("未处理的消息类型:", message);
        }
      }
    },
    // 格式化日期
    formatDate(dateString) {
      if (!dateString) return "未知时间";
      try {
        const date = new Date(dateString);
        return date.toLocaleString("zh-CN", {
          year: "numeric",
          month: "2-digit",
          day: "2-digit",
          hour: "2-digit",
          minute: "2-digit",
        });
      } catch (e) {
        return dateString;
      }
    },

    // 更新总条目数
    getdata() {
      this.isLoading = true;
      const order = {
        type: 10050,
        id: "getData",
        data: {
          current: this.currentPage,
          limit: this.pageSize,
          groupId: this.groupId, // 添加班组ID参数
        },
      };
      console.log("发送获取班组成员请求:", order);
      this.websocketService.send(order);
    },

    // 添加更新总条目数的方法
    updateTotalItems() {
      if (!this.totalItems) {
        this.totalItems = this.members.length;
      }
    },
    
    // 重置并重新加载数据
    resetAndReload() {
      this.currentPage = 1;
      this.members = [];
      this.allDataLoaded = false;
      this.getdata();
    },

    // 处理滚动事件，加载更多数据
    handleScroll() {
      const container = this.$refs.cardListContainer;
      if (!container) return;
      
      // 滚动到距离底部一定距离时，加载更多数据
      if (container.scrollTop + container.clientHeight >= container.scrollHeight - this.scrollThreshold) {
        if (!this.isLoading && !this.allDataLoaded) {
          this.currentPage++;
          this.getdata();
        }
      }
      
      // 更新回到顶部按钮的可见性
      this.toggleScrollTopButton();
    },
    
    // 显示/隐藏回到顶部按钮
    toggleScrollTopButton() {
      const container = this.$refs.cardListContainer;
      if (container) {
        this.showScrollTopBtn = container.scrollTop > 200;
      }
    },
    
    // 滚动到顶部
    scrollToTop() {
      const container = this.$refs.cardListContainer;
      if (container) {
        container.scrollTo({
          top: 0,
          behavior: 'smooth'
        });
      }
    },

    // 添加成员
    handleAddMember() {
      this.memberDialogTitle = "添加班组成员";
      this.isEditMode = false; // 设置为新增模式

      // 重置表单
      this.memberForm = {
        id: null,
        username: "",
        name: "",
        workId: "",
        position: "",
        phone: "",
        isGroupLeader: false,
        createTime: null,
        list: [],
      };
      // 清空表单验证
      if (this.$refs.memberForm) {
        this.$refs.memberForm.clearValidate();
      }
      // 获取可用用户列表
      this.getAvailableUsers();
      this.memberFormVisible = true;
    },

    // 编辑成员
    handleEditMember(row) {
      this.memberDialogTitle = "修改组员类型";
      this.isEditMode = true; // 设置为编辑模式
      // 只复制必要字段
      this.memberForm = {
        userName: row.userName,
        displayName: row.displayName,
        type: row.type,
        personId: row.personId,
        id: row.id,
        groupId: row.groupId,
        list: [], // 添加选择的用户数组
      };
        // 清空表单验证
        if (this.$refs.memberForm) {
        this.$refs.memberForm.clearValidate();
      }
      this.memberFormVisible = true;
    },

    // 删除成员
    handleDeleteMember(row) {
      this.deleteItem = row;
      this.deleteDialogVisible = true;
    },

    // 确认删除成员
    confirmDelete() {
      const order = {
        type: 10052,
        id: "confirmDelete",
        data: {
          id: this.deleteItem.id,
        },
      };
      console.log("发送获取班组成员请求:", order);
      this.websocketService.send(order);
    },

    // 提交成员表单
    submitMemberForm() {
      this.$refs.memberForm.validate((valid) => {
        if (valid) {
          // this.isEditMode
          if (this.memberForm.id && this.isEditMode) {
            const order = {
              type: 10054,
              id: "modifyMember",
              data: {
                ...this.memberForm,
              },
            };
            console.log("编辑组员类型请求:", order);
            this.websocketService.send(order);
          } else {
            // 添加新成员
            const saveData = this.availableUsers
        .filter((user) => this.memberForm.list.includes(user.id))
        .map((user) => ({
          groupId: this.groupId,
          personId: user.id,
        }));
            const order = {
              type: 10051,
              id: "addMember",
              data: {
                // groupId: this.groupId,
                list:saveData, // 选中的用户ID数组
              },
            };
            console.log("添加班组成员请求:", order);
            this.websocketService.send(order);
          }

          // 不要在这里关闭对话框，等待WebSocket响应后再关闭
          // this.memberFormVisible = false;
        }
      });
    },

    // 获取可用用户列表
    getAvailableUsers() {

      const order = {
        type: 10201,
        id: "getAvailableUsers",
        data: {
        },
      };
      this.websocketService.send(order);
    },
    getUserFindList(orgIds) {
      const order = {
        type: 10202,
        id: "getUserFindList",
        data: {
          orgIds
        },
      };
      this.websocketService.send(order);
    },
    getUserBindList() {
      const order = {
        type: 10053,
        id: "getUserBindList",
        data: {
          id:this.groupId
        }
      };
      this.websocketService.send(order);
    },
  },
};
</script>

<style scoped>
/* 卡片列表容器 */
.card-list-container {
  width: 100%;
  height: calc(100vh - 240px);
  overflow-y: auto;
  padding: 5px;
  border-radius: 8px;
  position: relative;
}

/* 卡片网格布局 */
.card-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
  gap: 20px;
  width: 100%;
  padding: 10px;
}

/* 横屏状态 - 四列布局 */
@media (min-width: 1600px) {
  .card-grid {
    grid-template-columns: repeat(4, 1fr);
  }
}

/* 中等屏幕 - 三列布局 */
@media (max-width: 1599px) and (min-width: 1200px) {
  .card-grid {
    grid-template-columns: repeat(3, 1fr);
  }
}

/* 平板状态 - 两列布局 */
@media (max-width: 1199px) and (min-width: 768px) {
  .card-grid {
    grid-template-columns: repeat(2, 1fr);
  }
}

/* 竖屏手机状态 - 单列布局 */
@media (max-width: 767px) {
  .card-grid {
    grid-template-columns: 1fr;
  }
}

/* 成员卡片样式 */
.member-card {
  display: flex;
  flex-direction: column;
  background-color: rgba(34, 44, 74, 0.8);
  border-radius: 12px;
  overflow: hidden;
  box-shadow: 0 4px 10px rgba(0, 0, 0, 0.3);
  backdrop-filter: blur(5px);
  border: 1px solid rgba(255, 255, 255, 0.1);
  transition: all 0.3s ease;
}

.member-card:hover {
  transform: translateY(-3px);
  box-shadow: 0 8px 15px rgba(0, 0, 0, 0.4);
}

.member-card-content {
  padding: 15px 20px;
  flex-grow: 1;
}

/* 卡片信息区 */
.info-row {
  display: flex;
  margin-bottom: 12px;
  align-items: flex-start;
  justify-content: space-between;
}

.info-label {
  color: rgba(255, 255, 255, 0.9);
  font-size: 14px;
  text-align: left;
}

.info-value {
  color: #ffffff;
  font-size: 14px;
  text-align: right;
  font-weight: 500;
  max-width: 60%;
}

/* 卡片操作区 */
.member-card-actions {
  display: flex;
  justify-content: space-between;
  padding: 10px;
  background-color: rgba(20, 27, 47, 0.6);
}

.action-btn {
  flex: 1;
  margin: 0 5px;
}

/* 没有数据提示 */
.no-data-indicator {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  padding: 40px 0;
  color: rgba(255, 255, 255, 0.6);
  gap: 10px;
}

.no-data-indicator i {
  font-size: 30px;
}

.no-data-indicator span {
  font-size: 14px;
}

/* 加载更多指示器 */
.loading-indicator {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 60px;
  color: #fff;
  font-size: 14px;
  gap: 8px;
  padding: 10px 0;
}

/* 全部加载完毕提示 */
.load-all-indicator {
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 20px 0;
  color: rgba(255, 255, 255, 0.6);
  font-size: 14px;
}

/* 美化搜索和添加按钮 */
.add-btn {
  border-radius: 20px !important;
  padding: 10px 20px !important;
  transition: all 0.3s ease;
  box-shadow: 0 4px 10px rgba(19, 206, 102, 0.3) !important;
}

.add-btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 6px 14px rgba(19, 206, 102, 0.4) !important;
}

/* 回到顶部按钮 */
.scroll-to-top-btn {
  position: fixed;
  bottom: 30px;
  right: 30px;
  width: 40px;
  height: 40px;
  border-radius: 50%;
  background-color: #4e77e5;
  color: white;
  display: flex;
  justify-content: center;
  align-items: center;
  cursor: pointer;
  z-index: 99;
  opacity: 0;
  visibility: hidden;
  transition: all 0.3s ease;
  box-shadow: 0 4px 10px rgba(0, 0, 0, 0.3);
}

.scroll-to-top-btn.visible {
  opacity: 1;
  visibility: visible;
}

.scroll-to-top-btn i {
  font-size: 20px;
}

.scroll-to-top-btn:hover {
  background-color: #3f68cd;
  transform: translateY(-3px);
  box-shadow: 0 6px 12px rgba(0, 0, 0, 0.4);
}

/* 标签样式 */
:deep(.el-tag--success) {
  background-color: rgba(19, 206, 102, 0.2) !important;
  border-color: rgba(19, 206, 102, 0.8) !important;
  color: #13ce66 !important;
}

:deep(.el-tag--info) {
  background-color: rgba(144, 147, 153, 0.2) !important;
  border-color: rgba(144, 147, 153, 0.8) !important;
  color: #909399 !important;
}

/* 对话框样式 */
.members-dialog {
  background-image: linear-gradient(rgba(0, 102, 255, 0.1) 1px, transparent 1px),
    linear-gradient(90deg, rgba(0, 102, 255, 0.1) 1px, transparent 1px);
  background-size: 20px 20px;
}

/* 添加回原来的对话框样式 */
:deep(.el-dialog) {
  background-color: #1c1f37 !important;
  border: 1px solid rgba(65, 105, 225, 0.5) !important;
  border-radius: 12px !important;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.5) !important;
  overflow: hidden !important;
}

:deep(.el-dialog__header) {
  background-color: rgba(65, 105, 225, 0.2) !important;
  padding: 15px 20px !important;
  border-bottom: 1px solid rgba(65, 105, 225, 0.3) !important;
}

:deep(.el-dialog__title) {
  color: #fff !important;
  font-weight: bold !important;
}

:deep(.el-dialog__body) {
  color: #fff !important;
  padding: 20px !important;
}

:deep(.el-dialog__footer) {
  border-top: 1px solid rgba(65, 105, 225, 0.3) !important;
  padding: 15px 20px !important;
}

:deep(.el-form-item__label) {
  color: #fff !important;
}

:deep(.el-input__inner) {
  background-color: rgba(0, 0, 0, 0.3) !important;
  border-color: rgba(65, 105, 225, 0.5) !important;
  color: #fff !important;
  transition: all 0.3s ease !important;
}

:deep(.el-input__inner:focus) {
  border-color: rgba(65, 105, 225, 0.8) !important;
  box-shadow: 0 0 10px rgba(65, 105, 225, 0.3) !important;
}

/* 顶部操作区域样式 */
.top-actions-container {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 5px 20px 15px;
  width: 100%;
  margin-bottom: 10px;
}

.left-actions {
  display: flex;
  align-items: center;
}

.right-actions {
  display: flex;
  align-items: center;
}

/* 分页信息样式 */
.pagination-info {
  display: flex;
  align-items: center;
  background-color: rgba(65, 105, 225, 0.2);
  border-radius: 20px;
  padding: 6px 15px;
  color: #fff;
  font-size: 14px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);
}

.pagination-info .divider {
  margin: 0 8px;
  color: rgba(255, 255, 255, 0.5);
}
</style> 