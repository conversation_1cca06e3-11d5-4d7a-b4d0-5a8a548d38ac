# 智能证件柜管理系统

基于Electron + Vue2 + Element UI开发的智能证件柜管理系统，支持人脸识别和指纹识别功能。

## 项目环境要求

- Node.js >= 16.0.0
- npm >= 8.0.0

## 安装与运行

### 安装依赖

```bash
# 使用npm安装
npm install --ignore-scripts

# 如果安装失败，可以尝试
npm install --no-optional
```

### 启动开发模式

```bash
npm run electron:serve
```

### 构建应用

```bash
# 构建Windows可执行文件
npm run electron:build

# 构建Linux DEB包
npm run electron:build:deb
```

### 生成应用图标

```bash
# 先确保项目根目录下有source-icon.png文件
# 你需要创建一个512x512像素的PNG图片作为应用图标源文件
npm run generate-icons

# 生成Linux所需的多尺寸图标
npm run prepare:linux-icons
```

## 跨平台构建指南

### 方案一：使用 Docker 构建（推荐）

Docker 方案提供了最可靠的跨平台构建环境，特别适合在 Windows 系统上构建 Linux 包。

#### 1. 安装 Docker

1. 下载并安装 [Docker Desktop for Windows](https://www.docker.com/products/docker-desktop)
2. 确保 Docker Desktop 正在运行

#### 2. 使用 Docker 构建

```bash
# 在 Windows PowerShell 或 CMD 中运行
# 切换到项目目录
cd path/to/idcardbox-electron

# 使用 electronuserland/builder 镜像构建
docker run --rm -it ^
 --env-file <(env | grep -iE 'DEBUG|NODE_|ELECTRON_|YARN_|NPM_|CI|CIRCLE|TRAVIS_|APPVEYOR_|CSC_|GH_|GITHUB_|BT_|AWS_|STRIP|BUILD_') ^
 --env ELECTRON_CACHE="/root/.cache/electron" ^
 --env ELECTRON_BUILDER_CACHE="/root/.cache/electron-builder" ^
 -v ${PWD}:/project ^
 -v ${PWD##*/}-node-modules:/project/node_modules ^
 -v ~/.cache/electron:/root/.cache/electron ^
 -v ~/.cache/electron-builder:/root/.cache/electron-builder ^
 electronuserland/builder:wine ^
 bash -c "npm install && npm run electron:build:deb"
```

或者使用更简单的命令：
```bash
# 创建构建脚本
echo docker run --rm -it -v %cd%:/project electronuserland/builder:wine bash -c "npm install && npm run electron:build:deb" > build-linux.cmd

# 运行构建脚本
build-linux.cmd
```

#### 3. 构建产物
构建完成后，可以在 `dist_electron` 目录下找到：
- 证件柜_[版本号]_[架构].deb：Linux 安装包
- 其他构建产物

### 方案二：使用 WSL 构建

如果已经安装了 WSL（Windows Subsystem for Linux），也可以使用 WSL 进行构建。

#### 1. WSL 环境准备

1. 确保已安装 WSL2：
```bash
# 在 PowerShell 中检查 WSL 版本
wsl --list --verbose
```

2. 在 WSL 中安装必要的依赖：
```bash
# 在 WSL 终端中运行
sudo apt-get update
sudo apt-get install -y rpm dpkg fakeroot
```

#### 2. 项目准备

为了避免 Windows 文件系统的限制，需要将项目复制到 WSL 的 Linux 文件系统中：

```bash
# 在 WSL 终端中运行
# 创建工作目录
mkdir -p ~/projects
cd ~/projects

# 复制项目（假设项目在 F 盘）
cp -r /mnt/f/vueWebItem/A_idcardbox/idcardbox-electron ./
cd idcardbox-electron

# 清理并重新安装依赖
rm -rf node_modules
npm install
```

#### 3. 构建项目

```bash
# 在 WSL 的项目目录中运行
npm run electron:build:deb
```

### 常见问题

#### Docker 构建常见问题

1. Docker 容器无法启动
- 确保 Docker Desktop 正在运行
- 检查是否有足够的磁盘空间
- 尝试重启 Docker Desktop

2. 构建过程中网络错误
```bash
# 设置 npm 镜像
npm config set registry https://registry.npmmirror.com
# 设置 electron 镜像
npm config set electron_mirror https://cdn.npmmirror.com/binaries/electron/
```

#### WSL 构建常见问题

1. 找不到 Python 环境
```bash
# 在 WSL 中安装 Python
sudo apt-get install python3
```

2. 构建权限问题
```bash
# 确保项目目录具有正确的权限
sudo chown -R $USER:$USER ~/projects/idcardbox-electron
```

### 验证构建结果

无论使用哪种方式构建，都可以通过以下命令验证 deb 包：

```bash
# 在 WSL 或 Linux 环境中运行
# 查看包信息
dpkg -I dist_electron/证件柜_*.deb

# 测试安装
sudo dpkg -i dist_electron/证件柜_*.deb
sudo apt-get install -f  # 安装依赖
```

## 贡献指南

[之前的内容保持不变...]

## 常见问题解决方案

### 1. 安装依赖时出现phantomjs-prebuilt相关错误

原因：phantomjs-prebuilt包在较新版本的Node.js上会出现兼容性问题，特别是在Node.js 16及以上版本。

解决方法：
- 已通过自定义图标生成脚本替代electron-icon-builder，移除了phantomjs-prebuilt依赖
- 使用sharp库代替原来的图标生成工具链

### 2. 如果仍然出现安装错误

```bash
# 尝试清理npm缓存
npm cache clean --force

# 使用--ignore-scripts跳过脚本执行
npm install --ignore-scripts

# 安装单个特定版本的依赖
npm install [package]@[version] --save-dev
```

### 3. source-icon.png图标文件不存在

如果你没有合适的应用图标，可以通过以下方式创建一个简单的临时图标：

1. 使用在线工具如 [Figma](https://www.figma.com/) 或 [Canva](https://www.canva.com/) 创建一个512x512像素的PNG图标
2. 或者使用第三方图标库如 [Icons8](https://icons8.com/) 或 [Flaticon](https://www.flaticon.com/) 下载图标
3. 将图标文件重命名为`source-icon.png`并放置在项目根目录

### 4. 项目文件结构

```
├── build/ # 构建输出目录
├── public/ # 静态资源
├── scripts/ # 自定义脚本
│   └── generate-icons.js # 图标生成脚本
├── src/ # 源代码
│   ├── assets/ # 资源文件
│   ├── components/ # 组件
│   ├── views/ # 页面
│   ├── App.vue # 主组件
│   ├── background.js # Electron主进程
│   ├── main.js # Vue入口
│   └── router.js # 路由配置
├── .gitignore # Git忽略文件
├── babel.config.js # Babel配置
├── package.json # 项目配置
└── README.md # 项目说明
```

## 功能特性

- 证件管理系统
- 人脸识别功能
- 指纹识别功能
- 多用户权限管理
- 日志记录
- 数据导出与备份
- 网络状态监控
- 在线/离线模式切换

# 智能证件柜管理系统 - 技术栈文档

## 📋 项目概述

智能证件柜管理系统是一个基于 Electron + Vue2 开发的桌面应用程序，集成了人脸识别、指纹识别、静脉识别等生物识别技术，为证件管理提供安全可靠的解决方案。

## 🛠️ 核心技术栈

### 前端技术栈

#### 🎯 核心框架
- **Vue.js 2.6.14** - 渐进式 JavaScript 框架
  - 组件化开发
  - 响应式数据绑定
  - 虚拟 DOM
  - 指令系统

#### 🎨 UI 框架与样式
- **Element UI 2.15.14** - 基于 Vue 2.0 的桌面端组件库
  - 丰富的组件生态
  - 完整的设计语言
  - 国际化支持
- **TailwindCSS 3.3.5** - 实用优先的 CSS 框架
  - 原子化 CSS 类
  - 响应式设计
  - 自定义主题配置
- **Font Awesome 6.7.2** - 图标字体库
  - 丰富的图标资源
  - 矢量图标支持

#### 🚦 路由与状态管理
- **Vue Router 3.6.5** - Vue.js 官方路由管理器
  - 嵌套路由映射
  - 模块化、基于组件的路由配置
  - 路由参数、查询、通配符
  - 基于 Vue.js 过渡系统的视图过渡效果
- **Vuex (自定义实现)** - 状态管理模式
  - 集中式存储管理
  - 可预测的状态变更

### 桌面应用技术

#### 🖥️ 跨平台桌面应用
- **Electron 22.3.27** - 使用 JavaScript、HTML 和 CSS 构建跨平台桌面应用
  - 主进程与渲染进程架构
  - 原生 API 访问能力
  - 跨平台兼容性 (Windows/Linux/macOS)
- **Electron Builder 23.6.0** - 完整的解决方案，用于打包和构建 Electron 应用
  - 自动更新支持
  - 代码签名
  - 多平台构建

#### 💾 数据存储
- **Electron Store 8.1.0** - Electron 应用的简单数据持久化
  - 加密存储支持
  - JSON Schema 验证
  - 迁移支持

### 网络通信技术

#### 🌐 HTTP 客户端
- **Axios 1.7.9** - 基于 Promise 的 HTTP 库
  - 请求和响应拦截器
  - 请求和响应数据转换
  - 自动转换 JSON 数据
  - 客户端防御 XSRF

#### 🔌 实时通信
- **WebSocket** - 双向通信协议
  - 实时数据传输
  - 低延迟通信
  - 事件驱动架构

### 硬件集成技术

#### 🔍 生物识别技术
- **人脸识别** - 基于 SeetaFace 引擎
  - SeetaFaceDetector600.dll - 人脸检测
  - SeetaFaceRecognizer610.dll - 人脸识别
  - SeetaFaceLandmarker600.dll - 人脸关键点检测
  - SeetaFaceAntiSpoofingX600.dll - 活体检测
  - SeetaQualityAssessor300.dll - 人脸质量评估

- **指纹识别** - 多厂商支持
  - 指纹采集与比对
  - 模板存储与管理

- **静脉识别** - 手指静脉识别
  - 静脉图像采集
  - 静脉特征提取与比对

#### 📶 网络管理
- **Node-WiFi 2.0.16** - WiFi 网络管理
  - 网络扫描
  - 连接管理
  - 状态监控

### 开发工具链

#### 🔧 构建工具
- **Vue CLI 5.0.0** - Vue.js 开发的标准工具
  - 项目脚手架
  - 插件系统
  - 开发服务器
- **Webpack** - 模块打包器
  - 代码分割
  - 热模块替换
  - 资源优化

#### 📝 代码质量
- **ESLint 8.0.3** - JavaScript 代码检查工具
  - 代码风格统一
  - 错误检测
  - 自动修复
- **Babel** - JavaScript 编译器
  - ES6+ 语法转换
  - 浏览器兼容性

#### 🎨 样式处理
- **PostCSS 8.4.31** - CSS 转换工具
  - 自动添加浏览器前缀
  - CSS 优化
- **Autoprefixer 10.4.16** - 自动添加 CSS 前缀

### 图像处理技术

#### 🖼️ 图像处理
- **Sharp 0.32.6** - 高性能图像处理库
  - 图像缩放
  - 格式转换
  - 图像优化
- **QRCode 1.5.4** - 二维码生成库
  - 二维码生成
  - 自定义样式

### 系统集成技术

#### 🗄️ 数据库
- **SQLite** - 轻量级数据库
  - 本地数据存储
  - 事务支持
  - 全文搜索

#### 🔐 安全技术
- **数据加密** - 敏感数据保护
  - AES 加密算法
  - 密钥管理
- **访问控制** - 权限管理
  - 角色基础访问控制
  - 操作日志记录

### 部署与分发

#### 📦 打包分发
- **NSIS** - Windows 安装程序制作工具
  - 自定义安装界面
  - 卸载程序生成
- **DEB 包** - Linux 系统安装包
  - Debian/Ubuntu 兼容
  - 依赖管理

#### 🐳 容器化支持
- **Docker** - 容器化部署
  - 跨平台构建环境
  - 一致性部署

## 🏗️ 项目架构

### 目录结构
```
src/
├── api/                    # API 接口层
├── assets/                 # 静态资源
│   ├── fonts/             # 字体文件
│   ├── icons/             # 图标资源
│   └── *.css              # 样式文件
├── components/             # 公共组件
├── config/                 # 配置文件
├── layouts/                # 布局组件
├── router/                 # 路由配置
├── utils/                  # 工具函数
│   ├── directives.js      # 自定义指令
│   ├── request.js         # HTTP 请求封装
│   ├── storage.js         # 存储工具
│   ├── websocket.js       # WebSocket 封装
│   └── wifiUtils.js       # WiFi 工具
├── views/                  # 页面组件
│   ├── actionType/        # 操作类型管理
│   ├── authorizecode/     # 授权码管理
│   ├── collect/           # 数据采集
│   ├── face/              # 人脸识别
│   ├── groupManage/       # 分组管理
│   ├── method/            # 识别方式
│   ├── pick/              # 取件管理
│   ├── save/              # 存件管理
│   ├── scheduling/        # 调度管理
│   ├── sysSetting/        # 系统设置
│   ├── userManage/        # 用户管理
│   └── vein/              # 静脉识别
├── wsServer/               # WebSocket 服务端
│   ├── *.dll              # 识别引擎库
│   ├── document_cabinet.exe # 服务端程序
│   └── model/             # 算法模型
├── App.vue                 # 根组件
├── background.js           # Electron 主进程
├── main.js                 # Vue 入口文件
└── preload.js             # Electron 预加载脚本
```

### 进程架构
```
┌─────────────────────────────────────────────────────────────┐
│                    Electron 应用架构                        │
├─────────────────────────────────────────────────────────────┤
│  主进程 (Main Process)                                      │
│  ├── background.js - 应用生命周期管理                       │
│  ├── 窗口管理 - BrowserWindow 创建与控制                    │
│  ├── 系统集成 - 文件系统、硬件访问                          │
│  └── IPC 通信 - 与渲染进程通信                              │
├─────────────────────────────────────────────────────────────┤
│  渲染进程 (Renderer Process)                                │
│  ├── Vue.js 应用 - 用户界面                                 │
│  ├── Element UI - 组件库                                    │
│  ├── TailwindCSS - 样式框架                                 │
│  └── preload.js - 安全的 API 暴露                           │
├─────────────────────────────────────────────────────────────┤
│  WebSocket 服务 (Native Service)                            │
│  ├── document_cabinet.exe - 本地服务                        │
│  ├── 生物识别引擎 - SeetaFace/指纹/静脉                     │
│  ├── 硬件驱动 - 摄像头/指纹仪/静脉仪                        │
│  └── 数据库 - SQLite 本地存储                               │
└─────────────────────────────────────────────────────────────┘
```

## 🔧 开发环境要求

### 系统要求
- **操作系统**: Windows 10/11, Ubuntu 18.04+, macOS 10.14+
- **Node.js**: >= 16.0.0
- **npm**: >= 8.0.0
- **Python**: 3.7+ (用于 native 模块编译)

### 开发工具推荐
- **IDE**: Visual Studio Code
- **调试**: Vue DevTools, Electron DevTools
- **版本控制**: Git
- **包管理**: npm/yarn

## 🚀 性能特性

### 应用性能
- **启动时间**: < 3 秒
- **内存占用**: < 200MB
- **响应时间**: < 100ms (UI 交互)

### 识别性能
- **人脸识别**: < 500ms
- **指纹识别**: < 300ms
- **静脉识别**: < 800ms

## 🔒 安全特性

### 数据安全
- **本地加密存储**
- **传输加密** (TLS/SSL)
- **访问控制**
- **操作审计**

### 隐私保护
- **生物特征模板化存储**
- **数据最小化原则**
- **用户授权机制**

## 🌍 国际化支持

### 多语言
- **中文** (简体/繁体)
- **英文**
- **可扩展语言包**

### 本地化
- **时间格式**
- **数字格式**
- **货币格式**

## 📈 扩展性

### 插件系统
- **识别算法插件**
- **硬件驱动插件**
- **UI 主题插件**

### API 接口
- **RESTful API**
- **WebSocket API**
- **硬件抽象层 API**

## 🔄 更新机制

### 自动更新
- **增量更新**
- **回滚机制**
- **更新通知**

### 版本管理
- **语义化版本**
- **更新日志**
- **兼容性检查**

## 最近更新

### 2024年网络状态栏优化

#### 问题描述
有时候页面加载时 `NetworkStatusBar` 组件的 `loadSystemSettings` 方法没有执行，导致系统设置无法正确加载。

#### 问题原因
1. **WebSocket 连接时机问题**: 组件在 `mounted` 时调用 `loadSystemSettings()`，但此时 WebSocket 可能还没有完全连接成功
2. **组件加载时机问题**: `NetworkStatusBar` 组件可能在 WebSocket 连接建立之前就已经挂载了
3. **依赖注入时机问题**: `websocketService` 通过 `inject` 注入，但在某些情况下可能还没有准备好

#### 解决方案
1. **添加 WebSocket 状态监听**: 在 `NetworkStatusBar` 组件中添加对 `wsStatus` 的监听，确保在连接成功后再调用 `loadSystemSettings`
2. **实现重试机制**: 添加最多3次重试机制，避免因网络问题导致的加载失败
3. **添加超时检查**: 设置5秒超时，如果请求没有响应则自动重试
4. **防止重复请求**: 添加 `settingsLoaded` 标志，避免重复加载系统设置
5. **App.vue 主动触发**: 在 App.vue 的 WebSocket 连接成功后主动调用系统设置加载

#### 修改文件
- `src/components/NetworkStatusBar.vue`: 添加状态监听和重试机制
- `src/App.vue`: 添加 `getSystemSettings` 方法并在连接成功后调用

#### 技术细节
```javascript
// 监听 WebSocket 连接状态变化
wsStatus: {
  handler(newStatus) {
    if (newStatus === true) {
      this.$nextTick(() => {
        setTimeout(() => {
          this.loadSystemSettings();
        }, 100);
      });
    }
  },
  immediate: true,
}
```

#### 调试信息
组件现在会输出详细的调试信息，包括：
- 组件挂载时的 WebSocket 状态
- 系统设置加载的各个阶段
- 重试次数和失败原因
- 成功加载后的确认信息

这些改进确保了系统设置能够可靠地加载，提升了用户体验和系统稳定性。

## 功能特点

- 人脸识别
- 指纹识别
- 实时状态显示
- 系统监控

## 开发环境要求

- Node.js >= 16.0.0
- Vue 2.6.14
- Electron 13.0.0

## 安装步骤

1. 克隆项目
```bash
git clone [项目地址]
```

2. 安装依赖
```bash
npm install
```

3. 运行开发服务器
```bash
npm run electron:serve
```

4. 构建应用
```bash
npm run electron:build
```

## 构建国产化支持包

### 构建Linux .deb包

为了支持国产化系统（基于Debian/Ubuntu的Linux发行版），本项目配置了.deb格式的打包支持。

#### 前置准备

1. 确保系统已安装构建.deb包所需的依赖：
```bash
# Ubuntu/Debian系统
sudo apt-get install -y rpm fakeroot dpkg

# CentOS/RHEL系统
sudo yum install -y rpm-build fakeroot dpkg-devel
```

2. 准备Linux打包所需的多尺寸图标：
```bash
# 生成Linux所需的不同尺寸图标
npm run prepare:linux-icons
```

#### 构建DEB包

使用以下命令构建.deb包：
```bash
# 使用预配置的命令构建Linux .deb包
npm run electron:build:deb

# 或者使用原始命令
npm run electron:build -- --linux deb
```

3. 构建完成后，.deb安装包将保存在`dist_electron`目录中，文件名格式为`证件柜_[版本号]_[架构].deb`

#### 安装和使用

在目标Linux系统（包括国产化系统如统信UOS、麒麟等）上，使用以下命令安装.deb包：
```bash
sudo dpkg -i dist_electron/*.deb
sudo apt-get install -f  # 安装依赖
```

安装后，可以通过应用程序菜单或运行以下命令启动应用：
```bash
/opt/证件柜/证件柜
```

#### 常见问题

1. **构建失败或缺少依赖**

   如果在非Linux系统上构建.deb包时出现问题，可以考虑在Docker容器中构建：
   ```bash
   # 使用Docker构建
   docker run --rm -it -v ${PWD}:/project -w /project node:16 npm run electron:build:deb
   ```

2. **图标显示问题**

   如果应用图标在Linux系统中不正确显示，确保已运行`npm run prepare:linux-icons`生成了所有需要的图标尺寸。

3. **启动问题**

   某些国产化系统可能需要额外的依赖。如果应用无法启动，尝试安装以下包：
   ```bash
   sudo apt-get install -y libgtk-3-0 libnotify4 libnss3 libxss1 xdg-utils
   ```

## 项目结构

```
src/
  ├── assets/         # 静态资源
  ├── components/     # 公共组件
  ├── router/        # 路由配置
  ├── views/         # 页面组件
  └── App.vue        # 根组件
```

## 使用说明

1. 启动应用后，系统会自动进入识别界面
2. 人脸识别：将面部对准摄像头
3. 指纹识别：将手指放置在指纹识别区

## 注意事项

- 请确保摄像头和指纹识别设备正常连接
- 建议在光线充足的环境下使用人脸识别功能
- 保持指纹识别区域清洁

## 许可证

[MIT License](LICENSE) 
手动替换idcardbox-electron\node_modules\vue-cli-plugin-electron-builder\node_modules\app-builder-lib\templates\nsis\include里面的StdUtil.nsh文件

## 构建输出说明

### 构建产物位置

1. **Windows 构建产物**
```
dist_electron/
├── win-unpacked/          # Windows 免安装版
│   ├── 证件柜.exe         # 主程序
│   └── [其他依赖文件]
├── 证件柜.exe             # Windows 便携版程序
└── 证件柜.exe.blockmap    # 增量更新文件
```

2. **Linux 构建产物**
```
dist_electron/
├── linux-unpacked/        # Linux 免安装版
│   ├── 证件柜             # 主程序
│   └── [其他依赖文件]
└── 证件柜_0.1.0_amd64.deb # Debian/Ubuntu 安装包
```

### 文件说明

1. **Windows 版本**
- `dist_electron/证件柜.exe`：便携版，可直接运行
- `dist_electron/win-unpacked/`：解压版，包含所有依赖

2. **Linux 版本**
- `dist_electron/证件柜_[版本号]_[架构].deb`：Debian/Ubuntu 安装包
- `dist_electron/linux-unpacked/`：解压版，可直接运行

### 构建位置说明

1. **在 Windows 系统中构建**
- 位置：项目目录下的 `dist_electron` 文件夹
- 完整路径示例：`F:\vueWebItem\A_idcardbox\idcardbox-electron\dist_electron`

2. **在 WSL 中构建**
- 如果在 WSL 的 Linux 文件系统中构建：
  - 位置：`~/projects/idcardbox-electron/dist_electron`
  - 完整路径示例：`/home/<USER>/projects/idcardbox-electron/dist_electron`
- 如果通过 `/mnt` 访问 Windows 文件系统：
  - 位置：`/mnt/f/vueWebItem/A_idcardbox/idcardbox-electron/dist_electron`

3. **使用 Docker 构建**
- 构建完成后，文件同样位于项目的 `dist_electron` 目录
- 由于 Docker 卷映射，文件会自动同步到主机对应目录

### 获取构建产物

1. **从 Windows 获取**
```bash
# 直接从 dist_electron 目录复制所需文件
```

2. **从 WSL 获取**
```bash
# 如果构建在 WSL 的 Linux 文件系统中
cp ~/projects/idcardbox-electron/dist_electron/证件柜_*.deb /mnt/c/Users/<USER>/Desktop/

# 或者使用 Windows 资源管理器访问
# 地址：\\wsl$\Ubuntu\home\用户名\projects\idcardbox-electron\dist_electron
```

3. **从 Docker 获取**
```bash
# 文件会自动同步到项目目录的 dist_electron 文件夹中
```

### 验证构建产物

1. **验证 DEB 包**
```bash
# 在 WSL 或 Linux 系统中运行
dpkg -I dist_electron/证件柜_*.deb  # 查看包信息
ldd dist_electron/linux-unpacked/证件柜  # 检查依赖关系
```

2. **测试安装**
```bash
# 在目标 Linux 系统上
sudo dpkg -i 证件柜_*.deb
sudo apt-get install -f  # 安装依赖
```

## 软键盘指令 (v-keyboard)

### 功能说明

`v-keyboard` 指令用于在输入框中集成软键盘功能，可以阻止系统默认键盘弹出，同时支持自定义软键盘的显示和隐藏。

### 使用方法

#### 基本用法

```vue
<el-input v-keyboard placeholder="请输入内容" />
```

#### 高级配置

```vue
<el-input 
  v-keyboard="{ 
    autoShow: true, 
    autoHide: true, 
    preventSystemKeyboard: true 
  }" 
  placeholder="请输入内容" 
/>
```

### 参数说明

| 参数 | 类型 | 默认值 | 说明 |
|------|------|--------|------|
| `autoShow` | Boolean | false | 是否在聚焦时自动显示软键盘 |
| `autoHide` | Boolean | true | 是否在失焦时自动隐藏软键盘 |
| `preventSystemKeyboard` | Boolean | true | 是否阻止系统默认键盘弹出 |

### 特性

1. **阻止系统键盘**: 当 `preventSystemKeyboard` 为 `true` 时，会阻止系统默认键盘弹出
2. **自定义触发**: 点击键盘图标按钮时，不会阻止事件，确保自定义软键盘能够正常显示
3. **事件隔离**: 使用 `isCustomTrigger` 标记来区分自定义触发和系统触发
4. **自动清理**: 组件销毁时自动清理所有事件监听器

### 使用示例

#### 用户管理对话框

```vue
<template>
  <el-form-item label="用户名" prop="account">
    <el-input 
      v-model="form.account" 
      v-keyboard="{ autoShow: true, autoHide: true, preventSystemKeyboard: true }" 
      placeholder="请输入用户名"
    />
  </el-form-item>
  
  <el-form-item label="手机号" prop="mobile">
    <el-input 
      v-model="form.mobile" 
      v-keyboard="{ autoShow: true, autoHide: true, preventSystemKeyboard: true }" 
      placeholder="请输入手机号"
    />
  </el-form-item>
</template>
```

#### 授权码输入

```vue
<template>
  <input
    v-for="(_, index) in 5"
    :key="index"
    type="text"
    v-keyboard="{ autoShow: true, autoHide: false, preventSystemKeyboard: true }"
    @input="handleInput($event, index)"
    @keydown.delete="handleDelete($event, index)"
    ref="inputs"
  />
</template>
```

### 注意事项

1. 该指令依赖于 `window.electronAPI.openOsk()` 和 `window.electronAPI.closeOsk()` 方法
2. 在移动设备上，建议设置 `preventSystemKeyboard: true` 来避免系统键盘干扰
3. 指令会自动添加一个键盘图标按钮，点击可以手动切换软键盘显示状态
4. 当软键盘可见时，允许正常的输入操作

### 技术实现

- 使用 Vue 自定义指令实现
- 通过事件监听器阻止系统默认行为
- 使用 `isCustomTrigger` 标记区分事件来源
- 支持触摸设备和桌面设备
- 自动清理事件监听器，避免内存泄漏