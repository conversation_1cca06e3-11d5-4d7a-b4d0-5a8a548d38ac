<!-- 柜子格子状态修改对话框组件 -->
<template>
  <div v-if="visible" class="dialog-overlay" @click="onCancel">
    <div class="dialog-container scale-in" @click.stop>
      <div class="dialog-header">
        <h3 class="dialog-title">修改格子状态</h3>
        <button class="close-btn" @click="onCancel">
          <i class="fas fa-times"></i>
        </button>
      </div>
      
      <div class="dialog-content">
        <div class="box-info">
          <div class="box-number-display">
            <span class="box-label">格子编号:</span>
            <span class="box-number">{{ boxData.gridName }}</span>
          </div>
          <div class="current-status">
            <span class="status-label">当前状态:</span>
            <span class="status-text" :class="getStatusClass(boxData.status)">
              {{ getStatusText(boxData.status) }}
            </span>
          </div>
        </div>

        <div class="status-selection">
          <label class="selection-label">选择操作:</label>
          <div class="status-options">
            <label
              v-for="status in availableOptions"
              :key="status.value"
              class="status-option"
              :class="{ 'selected': selectedStatus === status.value }"
            >
              <input
                type="radio"
                :value="status.value"
                v-model="selectedStatus"
                class="status-radio"
              />
              <div class="status-circle" :class="status.class"></div>
              <span class="status-name">{{ status.label }}</span>
            </label>
          </div>
        </div>
      </div>

      <div class="dialog-actions">
        <button class="tech-button outline" @click="onCancel">
          取消
        </button>
        <button
          class="tech-button"
          @click="onConfirm"
          :disabled="!selectedStatus || (boxData.status !== 4 && selectedStatus === boxData.status)"
        >
          确认修改
        </button>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'BoxStatusDialog',
  props: {
    // 控制对话框是否可见
    visible: {
      type: Boolean,
      default: false
    },
    // 柜子数据
    boxData: {
      type: Object,
      default: () => ({})
    }
  },
  data() {
    return {
      selectedStatus: null,
      allStatusOptions: [
        { value: 1, label: '空置中', class: 'vacant' },
        { value: 2, label: '占用中', class: 'occupying' },
        { value: 3, label: '开启中', class: 'opening' },
        { value: 4, label: '故障中', class: 'fault' }
      ]
    };
  },
  computed: {
    // 根据当前状态动态生成可用选项
    availableOptions() {
      if (this.boxData.status === 4) {
        // 如果当前是故障状态，显示解除故障选项（恢复到空置状态）
        return [
          { value: 1, label: '解除故障', class: 'vacant' }
        ];
      } else {
        // 如果当前不是故障状态，显示设置故障选项和解除故障选项
        return [
          { value: 4, label: '设置故障', class: 'fault' },
          { value: 1, label: '解除故障', class: 'vacant' }
        ];
      }
    }
  },
  watch: {
    visible(newVal) {
      if (newVal) {
        // 对话框打开时，根据当前状态设置默认选中
        if (this.boxData.status === 4) {
          // 故障状态默认选中解除故障
          this.selectedStatus = 1;
        } else {
          // 非故障状态默认选中设置故障
          this.selectedStatus = 4;
        }
      }
    }
  },
  methods: {
    // 获取状态文本
    getStatusText(status) {
      switch (status) {
        case 1:
          return "空置中";
        case 2:
          return "占用中";
        case 3:
          return "开启中";
        case 4:
          return "故障中";
        default:
          return "未知状态";
      }
    },
    
    // 获取状态样式类
    getStatusClass(status) {
      switch (status) {
        case 1:
          return "vacant";
        case 2:
          return "occupying";
        case 3:
          return "opening";
        case 4:
          return "fault";
        default:
          return "";
      }
    },
    
    // 取消操作
    onCancel() {
      this.$emit('cancel');
    },
    
    // 确认操作
    onConfirm() {
      if (this.selectedStatus !== this.boxData.status) {
        this.$emit('confirm', {
          boxData: this.boxData,
          newStatus: this.selectedStatus
        });
      }
    }
  }
};
</script>

<style scoped>
.dialog-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.7);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1000;
  backdrop-filter: blur(5px);
}

.dialog-container {
  background: linear-gradient(135deg, rgba(0, 102, 255, 0.1), rgba(0, 163, 255, 0.1));
  border: 2px solid;
  border-image-slice: 1;
  border-image-source: linear-gradient(45deg, #0066ff, #00a3ff);
  border-radius: 12px;
  width: 90%;
  max-width: 500px;
  min-width: 280px;
  backdrop-filter: blur(10px);
  box-shadow: 0 20px 40px rgba(0, 102, 255, 0.3);
  margin: 0 auto;
}

.dialog-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px 20px 12px;
  border-bottom: 1px solid rgba(0, 102, 255, 0.3);
}

.dialog-title {
  color: #e2e8f0;
  font-size: clamp(16px, 4vw, 18px);
  font-weight: 600;
  margin: 0;
}

.close-btn {
  background: none;
  border: none;
  color: #a0aec0;
  font-size: 18px;
  cursor: pointer;
  padding: 4px;
  border-radius: 4px;
  transition: all 0.3s ease;
}

.close-btn:hover {
  color: #0066ff;
  background-color: rgba(0, 102, 255, 0.1);
}

.dialog-content {
  padding: 20px;
}

.box-info {
  margin-bottom: 20px;
  padding: 12px 16px;
  background-color: rgba(0, 0, 0, 0.2);
  border-radius: 8px;
  border: 1px solid rgba(0, 102, 255, 0.2);
}

.box-number-display {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 10px;
  flex-wrap: wrap;
  gap: 8px;
}

.box-label, .status-label {
  color: #a0aec0;
  font-size: clamp(12px, 3vw, 14px);
}

.box-number {
  color: #0066ff;
  font-size: clamp(16px, 4vw, 18px);
  font-weight: bold;
}

.current-status {
  display: flex;
  justify-content: space-between;
  align-items: center;
  flex-wrap: wrap;
  gap: 8px;
}

.status-text {
  font-weight: 500;
  padding: 4px 8px;
  border-radius: 4px;
  font-size: clamp(12px, 3vw, 14px);
}

.status-text.vacant {
  color: #22c55e;
  background-color: rgba(34, 197, 94, 0.1);
}

.status-text.occupying {
  color: #ef4444;
  background-color: rgba(239, 68, 68, 0.1);
}

.status-text.opening {
  color: #eab308;
  background-color: rgba(234, 179, 8, 0.1);
}

.status-text.fault {
  color: #9333ea;
  background-color: rgba(147, 51, 234, 0.1);
}

.status-selection {
  margin-bottom: 20px;
}

.selection-label {
  display: block;
  color: #e2e8f0;
  font-size: clamp(14px, 3.5vw, 16px);
  font-weight: 500;
  margin-bottom: 12px;
}

.status-options {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.status-option {
  display: flex;
  align-items: center;
  padding: 10px 12px;
  border: 2px solid transparent;
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.3s ease;
  background-color: rgba(0, 0, 0, 0.1);
}

.status-option:hover {
  border-color: rgba(0, 102, 255, 0.3);
  background-color: rgba(0, 102, 255, 0.05);
}

.status-option.selected {
  border-color: #0066ff;
  background-color: rgba(0, 102, 255, 0.1);
}

.status-radio {
  display: none;
}

.status-circle {
  width: 14px;
  height: 14px;
  border-radius: 50%;
  margin-right: 10px;
  border: 2px solid;
  flex-shrink: 0;
}

.status-circle.vacant {
  border-color: #22c55e;
  background-color: rgba(34, 197, 94, 0.2);
}

.status-circle.occupying {
  border-color: #ef4444;
  background-color: rgba(239, 68, 68, 0.2);
}

.status-circle.opening {
  border-color: #eab308;
  background-color: rgba(234, 179, 8, 0.2);
}

.status-circle.fault {
  border-color: #9333ea;
  background-color: rgba(147, 51, 234, 0.2);
}

.status-name {
  color: #e2e8f0;
  font-size: clamp(12px, 3vw, 14px);
  font-weight: 500;
}

.dialog-actions {
  display: flex;
  justify-content: flex-end;
  gap: 10px;
  padding: 12px 20px 20px;
  border-top: 1px solid rgba(0, 102, 255, 0.3);
  flex-wrap: wrap;
}

.tech-button {
  padding: 8px 16px;
  background: linear-gradient(45deg, #0066ff, #00a3ff);
  color: white;
  border: none;
  border-radius: 6px;
  cursor: pointer;
  font-weight: 500;
  transition: all 0.3s ease;
  min-width: 70px;
  font-size: clamp(12px, 3vw, 14px);
}

.tech-button:hover:not(:disabled) {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 102, 255, 0.4);
}

.tech-button:disabled {
  opacity: 0.5;
  cursor: not-allowed;
  transform: none;
  box-shadow: none;
}

.tech-button.outline {
  background: transparent;
  border: 1px solid #0066ff;
  color: #0066ff;
}

.tech-button.outline:hover {
  background-color: rgba(0, 102, 255, 0.1);
}

.scale-in {
  animation: scaleIn 0.3s ease-out;
}

@keyframes scaleIn {
  from {
    transform: scale(0.9);
    opacity: 0;
  }
  to {
    transform: scale(1);
    opacity: 1;
  }
}

/* 响应式设计 - 移动设备 */
@media (max-width: 480px) {
  .dialog-container {
    width: 95%;
    min-width: 260px;
    margin: 10px;
  }

  .dialog-header {
    padding: 12px 16px 10px;
  }

  .dialog-content {
    padding: 16px;
  }

  .box-info {
    padding: 10px 12px;
    margin-bottom: 16px;
  }

  .box-number-display,
  .current-status {
    flex-direction: column;
    align-items: flex-start;
    gap: 4px;
  }

  .status-option {
    padding: 8px 10px;
  }

  .dialog-actions {
    padding: 10px 16px 16px;
    gap: 8px;
  }

  .tech-button {
    padding: 6px 12px;
    min-width: 60px;
  }
}

/* 响应式设计 - 平板设备 */
@media (min-width: 481px) and (max-width: 768px) {
  .dialog-container {
    width: 85%;
    max-width: 450px;
  }

  .dialog-header {
    padding: 14px 18px 12px;
  }

  .dialog-content {
    padding: 18px;
  }
}

/* 响应式设计 - 大屏设备 */
@media (min-width: 1200px) {
  .dialog-container {
    max-width: 520px;
  }

  .dialog-header {
    padding: 22px 26px 18px;
  }

  .dialog-content {
    padding: 26px;
  }

  .box-info {
    padding: 18px 20px;
    margin-bottom: 26px;
  }

  .status-selection {
    margin-bottom: 26px;
  }

  .dialog-actions {
    padding: 18px 26px 26px;
  }
}
</style>
