<!-- 系统设置页面 - 离线/在线模式切换 -->

<template>
  <div class="min-h-screen tech-grid bg-gray-900">
    <Logo class="absolute top-8 left-8 z-40" />
    <SoftKeyboard :autoInitialize="false"></SoftKeyboard>

    <!-- 使用提取出的用户信息组件 -->
    <UserInfoBar  class="absolute bottom-8 right-8 z-40" />
    <!-- 返回按钮 -->
    <div class="absolute top-8 right-8 z-50" @click="goHome">
      <button
        class="text-white flex items-center space-x-2 !rounded-button whitespace-nowrap hover:opacity-80 cursor-pointer p-2"
      >
        <i class="fas fa-home text-lg"></i>
        <span>返回主页</span>
      </button>
    </div>

    <div
      class="container mx-auto px-4 h-screen flex flex-col items-center justify-center"
    >
      <!-- 标题 -->
      <div class="w-full max-w-md">
        <!-- 登录卡片 -->
        <div v-if="!isLoggedIn" class="tech-card p-6 mb-4">
          <h2 class="text-lg font-semibold mb-6 text-white text-center">
            管理员登录
          </h2>

          <div class="form-group">
            <label for="username"><span class="required">*</span> 账号:</label>
            <input
              type="text"
              id="username"
              v-model="loginForm.username"
              placeholder="请输入管理员账号"
              class="tech-input"
            />
          </div>

          <div class="form-group">
            <label for="password"><span class="required">*</span> 密码:</label>
            <input
              type="password"
              id="password"
              v-model="loginForm.password"
              placeholder="请输入管理员密码"
              class="tech-input"
            />
          </div>

          <div class="form-actions">
            <button class="tech-button" @click="handleLogin">登录</button>
          </div>
        </div>

        <!-- 设置卡片 - 仅在登录后显示 -->
       <div v-else>
          <!-- Tab选项卡 -->
          <div class="tech-tabs mb-4">
            <div 
              class="tech-tab" 
              :class="{ 'active': activeTab === 'admin' }" 
              @click="activeTab = 'admin'"
            >
              管理员开柜
            </div>
            <div 
              class="tech-tab" 
              :class="{ 'active': activeTab === 'system' }" 
              @click="activeTab = 'system'"
            >
              系统模式
            </div>

            <div 
              class="tech-tab" 
              :class="{ 'active': activeTab === 'modifyPassword' }" 
              @click="activeTab = 'modifyPassword'"
            >
              修改密码
            </div>

          </div>

          <!--管理员开柜-->
            <div v-show="activeTab === 'admin'" class="tech-card p-6 mb-4">
              <!-- 管理员开柜内容 -->
              <h2 class="text-lg font-semibold mb-6 text-white text-center">
                管理员开柜
              </h2>
              
              <!-- 柜子格子布局 -->
              <div class="grid-container">
                <div
                  v-for="(box, index) in displayedBoxList"
                  :key="index"
                  class="grid-box"
                  :class="{
                    'vacant': box.status === 1,
                    'occupying': box.status === 2,
                    'opening': box.status === 3,
                    'fault': box.status === 4,
                    'long-pressing': longPressBox && longPressBox.gridCode === box.gridCode
                  }"
                  @click="handleBoxClick(box)"
                  @mousedown="handleMouseDown(box)"
                  @mouseup="handleMouseUp"
                  @mouseleave="handleMouseLeave"
                  @touchstart="handleTouchStart(box)"
                  @touchend="handleTouchEnd"
                  @touchcancel="handleTouchCancel"
                >
                  <div class="box-number">{{ box.gridName }}</div>
                  <div class="box-status">{{ getStatusText(box.status) }}</div>
                  <!-- 长按进度指示器 -->
                  <div v-if="longPressBox && longPressBox.gridCode === box.gridCode" class="long-press-indicator">
                    <div class="progress-ring">
                      <svg class="progress-svg" width="60" height="60">
                        <circle
                          class="progress-circle-bg"
                          cx="30"
                          cy="30"
                          r="25"
                          fill="none"
                          stroke="rgba(0, 102, 255, 0.2)"
                          stroke-width="3"
                        />
                        <circle
                          class="progress-circle"
                          cx="30"
                          cy="30"
                          r="25"
                          fill="none"
                          stroke="#0066ff"
                          stroke-width="3"
                          stroke-linecap="round"
                          :style="{ strokeDasharray: progressCircumference, strokeDashoffset: progressOffset }"
                        />
                      </svg>
                      <i class="fas fa-edit progress-icon"></i>
                    </div>
                  </div>
                </div>
              </div>

              <!-- 分页控制 -->
              <div class="pagination-container">
                <button 
                  class="pagination-btn" 
                  :disabled="currentPage === 1"
                  @click="changePage(currentPage - 1)"
                >
                  <i class="fas fa-chevron-left"></i>
                </button>
                <span class="pagination-text">{{ currentPage }} / {{ totalPages }}</span>
                <button 
                  class="pagination-btn" 
                  :disabled="currentPage === totalPages"
                  @click="changePage(currentPage + 1)"
                >
                  <i class="fas fa-chevron-right"></i>
                </button>
              </div>

              <!-- 操作按钮 -->
              <div class="form-actions">
                <button class="tech-button" @click="hanldeOpenAllBox">打开全部柜格</button>
              </div>
              <!-- 这里可以添加管理员开柜的功能 -->
            </div>
          <!--管理员开柜-->

            <!--系统模式-->
            <div v-show="activeTab === 'system'">
              <div class="tech-card p-6 mb-4">
          <!-- 模式选择单选框 -->
          <div class="form-group">
            <label><span class="required">*</span> 系统模式:</label>
            <div class="tech-radio-container">
              <label class="radio-option">
                <input
                  type="radio"
                  v-model="serverSettings.offline"
                  :value="0"
                />
                <span class="radio-circle"></span>
                <span class="radio-text">离线</span>
              </label>
              <label class="radio-option">
                <input
                  type="radio"
                  v-model="serverSettings.offline"
                  :value="1"
                />
                <span class="radio-circle"></span>
                <span class="radio-text">在线</span>
              </label>
            </div>
          </div>

          <!-- 在线模式附加设置 -->
          <div v-if="serverSettings.offline === 1">
            <div class="form-group">
              <label for="serverIp"
                ><span class="required">*</span> 服务器IP:</label
              >
              <input
                type="text"
                id="serverIp"
                v-model="serverSettings.serverIp"
                placeholder="输入服务器IP或域名"
                class="tech-input"
                @input="
                  (e) => {
                    // 允许IP地址和域名格式输入
                    serverSettings.serverIp = e.target.value;
                  }
                "
              />
            </div>

            <div class="form-group">
              <label for="serverPort"
                ><span class="required">*</span> 服务器端口:</label
              >
              <input
                type="text"
                id="serverPort"
                v-model="serverSettings.serverPort"
                placeholder="输入服务器端口"
                class="tech-input"
                @input="
                  (e) => {
                    // 只允许输入数字
                    e.target.value = e.target.value.replace(/\D/g, '');
                    serverSettings.serverPort = e.target.value;
                  }
                "
              />
            </div>

            <div class="form-group">
              <label for="serverHttpPort"
                ><span class="required">*</span> HTTP端口:</label
              >
              <input
                type="text"
                id="serverHttpPort"
                v-model="serverSettings.serverHttpPort"
                placeholder="输入HTTP端口"
                class="tech-input"
                @input="
                  (e) => {
                    // 只允许输入数字
                    e.target.value = e.target.value.replace(/\D/g, '');
                    serverSettings.serverHttpPort = e.target.value;
                  }
                "
              />
            </div>

            <div class="form-group">
              <label for="serverHttps"
                ><span class="required">*</span> 是否启用HTTPS:</label
              >
              <div class="tech-radio-container">
                <label class="radio-option">
                  <input type="radio" v-model="serverSettings.serverHttps" :value="0" />
                  <span class="radio-circle"></span>
                  <span class="radio-text">不启用</span>
                </label>
                <label class="radio-option">
                  <input type="radio" v-model="serverSettings.serverHttps" :value="1" />
                  <span class="radio-circle"></span>
                  <span class="radio-text">启用</span>
                </label>
              </div>
            </div>

            <div class="form-group">
              <label for="offline"
                ><span class="required">*</span> 服务端SSL:</label
              >
              <div class="tech-radio-container">
                <label class="radio-option">
                  <input type="radio" v-model="serverSettings.ssl" :value="0" />
                  <span class="radio-circle"></span>
                  <span class="radio-text">禁用</span>
                </label>
                <label class="radio-option">
                  <input type="radio" v-model="serverSettings.ssl" :value="1" />
                  <span class="radio-circle"></span>
                  <span class="radio-text">启用</span>
                </label>
              </div>
            </div>
          </div>

          <div v-else>

            <!-- 创建管理员按钮 -->
            
            <div class="form-group" v-if="currentStateConfig === 0">
              <label>管理员管理:</label>
              <div class="form-actions">
                <button class="tech-button" @click="handleCreateAdmin">创建管理员</button>
                <button class="tech-button outline" @click="openAdminDrawer">更多</button>
              </div>
            </div>

          </div>

          <!-- 表单操作按钮 -->
          <div class="form-actions">
            <button class="tech-button outline" @click="resetSettings">
              重置
            </button>
            <button class="tech-button" @click="saveSettings">保存</button>
          </div>
        </div>
            </div>
            <!--系统模式-->

            <!--修改密码-->
            <div v-show="activeTab === 'modifyPassword'" class="tech-card p-6 mb-4">
              <h2 class="text-lg font-semibold mb-6 text-white text-center">
                修改密码
              </h2>

              <div class="form-group">
                <label for="oldPassword"><span class="required">*</span> 原密码:</label>
                <input
                  type="password"
                  id="oldPassword"
                  v-model="passwordForm.oldPassword"
                  placeholder="请输入原密码"
                  class="tech-input"
                />
              </div>

              <div class="form-group">
                <label for="newPassword"><span class="required">*</span> 新密码:</label>
                <input
                  type="password"
                  id="newPassword"
                  v-model="passwordForm.newPassword"
                  placeholder="请输入新密码"
                  class="tech-input"
                  @input="validatePasswordStrength"
                />
                <!-- 密码强度提示 -->
                <div class="password-requirements">
                  <div class="requirement-title">密码要求（高强度）：</div>
                  <div class="requirement-list">
                    <div class="requirement-item" :class="{ 'valid': passwordValidation.length }">
                      <i class="fas" :class="passwordValidation.length ? 'fa-check' : 'fa-times'"></i>
                      长度至少8位
                    </div>
                    <div class="requirement-item" :class="{ 'valid': passwordValidation.uppercase }">
                      <i class="fas" :class="passwordValidation.uppercase ? 'fa-check' : 'fa-times'"></i>
                      包含大写字母
                    </div>
                    <div class="requirement-item" :class="{ 'valid': passwordValidation.lowercase }">
                      <i class="fas" :class="passwordValidation.lowercase ? 'fa-check' : 'fa-times'"></i>
                      包含小写字母
                    </div>
                    <div class="requirement-item" :class="{ 'valid': passwordValidation.number }">
                      <i class="fas" :class="passwordValidation.number ? 'fa-check' : 'fa-times'"></i>
                      包含数字
                    </div>
                    <div class="requirement-item" :class="{ 'valid': passwordValidation.special }">
                      <i class="fas" :class="passwordValidation.special ? 'fa-check' : 'fa-times'"></i>
                      包含特殊符号 (!@#$%^&amp;*()_+-=[]{}|;:,.&lt;&gt;?)
                    </div>
                  </div>
                  <!-- 密码强度指示器 -->
                  <div class="password-strength-indicator">
                    <div class="strength-label">密码强度：</div>
                    <div class="strength-bar">
                      <div class="strength-fill" :class="passwordStrengthClass" :style="{ width: passwordStrengthPercent + '%' }"></div>
                    </div>
                    <div class="strength-text" :class="passwordStrengthClass">{{ passwordStrengthText }}</div>
                  </div>
                </div>
              </div>

              <div class="form-group">
                <label for="confirmPassword"><span class="required">*</span> 再次确认密码:</label>
                <input
                  type="password"
                  id="confirmPassword"
                  v-model="passwordForm.confirmPassword"
                  placeholder="请再次输入新密码"
                  class="tech-input"
                />
              </div>

              <!-- 表单操作按钮 -->
              <div class="form-actions">
                <button class="tech-button outline" @click="resetPasswordForm">
                  重置
                </button>
                <button class="tech-button" @click="handleChangePassword">修改密码</button>
              </div>
            </div>
             <!--修改密码-->
       </div>


   



      </div>
    </div>

    <!-- 格子状态修改对话框 -->
    <BoxStatusDialog
      :visible="showStatusDialog"
      :boxData="selectedBoxForEdit"
      @cancel="cancelStatusEdit"
      @confirm="confirmStatusEdit"
    />

    <!-- 创建管理员对话框 -->

    <!-- 管理员管理抽屉 -->
    <el-drawer
      title="管理员管理"
      :visible.sync="adminDrawerVisible"
      direction="rtl"
      size="50%"
      :before-close="handleDrawerClose"
      custom-class="admin-drawer"
    >
      <div class="drawer-content">
        <h3 class="drawer-title">管理员功能</h3>
        <div class="drawer-menu">
          <div class="menu-item" @click="handleCreateAdmin">
            <i class="fas fa-user-plus"></i>
            <span>创建管理员</span>
          </div>
          <div class="menu-item" @click="handleManageAdmin">
            <i class="fas fa-users-cog"></i>
            <span>管理员列表</span>
          </div>
          <div class="menu-item" @click="handleSystemUpgrade">
            <i class="fas fa-upload"></i>
            <span>升级</span>
          </div>
          <div class="menu-item" @click="handleImportOrganization">
            <i class="fas fa-sitemap"></i>
            <span>导入组织</span>
          </div>
          <div class="menu-item" @click="handleImportRole">
            <i class="fas fa-user-tag"></i>
            <span>导入角色</span>
          </div>
        </div>
      </div>
    </el-drawer>

    <!-- 隐藏的文件输入框 -->
    <input
      ref="upgradeFileInput"
      type="file"
      accept=".zip,.rar,.7z,.tar,.gz"
      style="display: none"
      @change="handleUpgradeFileSelect"
    />
    <input
      ref="organizationFileInput"
      type="file"
      accept=".xlsx,.xls,.csv"
      style="display: none"
      @change="handleOrganizationFileSelect"
    />
    <input
      ref="roleFileInput"
      type="file"
      accept=".xlsx,.xls,.csv"
      style="display: none"
      @change="handleRoleFileSelect"
    />

    <!-- 升级对话框 -->
    <el-dialog
      title="系统升级"
      :visible.sync="upgradeDialogVisible"
      width="40%"
      custom-class="import-dialog"
    >
      <div class="import-container">
        <div class="import-btn-container text-center mb-4">
          <el-button
            type="primary"
            @click="selectUpgradeFile"
            class="import-select-btn"
          >
            <i class="el-icon-folder-opened mr-2"></i>选择升级包
          </el-button>
          <div v-if="selectedUpgradeFilePath" class="selected-file-path mt-3">
            <p class="text-truncate">当前选择: {{ selectedUpgradeFilePath }}</p>
          </div>
        </div>

        <div class="import-tips mt-4">
          <p class="text-warning mb-2">
            <i class="el-icon-warning mr-1"></i>升级说明：
          </p>
          <ul class="ml-4">
            <li>1. 请选择压缩文件(.zip/.rar/.7z/.tar/.gz)</li>
            <li>2. 升级包必须是官方提供的版本</li>
            <li>3. 升级过程中请勿关闭系统</li>
            <li>4. 升级完成后系统将自动重启</li>
          </ul>
        </div>
      </div>
      <span slot="footer" class="dialog-footer">
        <el-button @click="upgradeDialogVisible = false">取 消</el-button>
        <el-button
          type="primary"
          @click="performUpgrade"
          :disabled="!selectedUpgradeFilePath"
          >开始升级</el-button
        >
      </span>
    </el-dialog>

    <!-- 导入组织对话框 -->
    <el-dialog
      title="导入组织信息"
      :visible.sync="importOrgDialogVisible"
      width="40%"
      custom-class="import-dialog"
    >
      <div class="import-container">
        <div class="import-btn-container text-center mb-4">
          <el-button
            type="primary"
            @click="selectOrgFile"
            class="import-select-btn"
          >
            <i class="el-icon-folder-opened mr-2"></i>选择文件
          </el-button>
          <div v-if="selectedOrgFilePath" class="selected-file-path mt-3">
            <p class="text-truncate">当前选择: {{ selectedOrgFilePath }}</p>
          </div>
        </div>

        <div class="import-tips mt-4">
          <p class="text-warning mb-2">
            <i class="el-icon-warning mr-1"></i>导入说明：
          </p>
          <ul class="ml-4">
            <li>1. 请选择Excel文件(.xlsx/.xls)或CSV文件(.csv)</li>
            <li>2. 文件必须包含组织名称、组织代码等必要字段</li>
            <li>3. 导入数据将仅在本地存储，连接网络后将自动同步</li>
            <li>4. 路径不能用中文</li>
          </ul>
        </div>
      </div>
      <span slot="footer" class="dialog-footer">
        <el-button @click="importOrgDialogVisible = false">取 消</el-button>
        <el-button
          type="primary"
          @click="importOrganization"
          :disabled="!selectedOrgFilePath"
          >导 入</el-button
        >
      </span>
    </el-dialog>

    <!-- 导入角色对话框 -->
    <el-dialog
      title="导入角色信息"
      :visible.sync="importRoleDialogVisible"
      width="40%"
      custom-class="import-dialog"
    >
      <div class="import-container">
        <div class="import-btn-container text-center mb-4">
          <el-button
            type="primary"
            @click="selectRoleFile"
            class="import-select-btn"
          >
            <i class="el-icon-folder-opened mr-2"></i>选择文件
          </el-button>
          <div v-if="selectedRoleFilePath" class="selected-file-path mt-3">
            <p class="text-truncate">当前选择: {{ selectedRoleFilePath }}</p>
          </div>
        </div>

        <div class="import-tips mt-4">
          <p class="text-warning mb-2">
            <i class="el-icon-warning mr-1"></i>导入说明：
          </p>
          <ul class="ml-4">
            <li>1. 请选择Excel文件(.xlsx/.xls)或CSV文件(.csv)</li>
            <li>2. 文件必须包含角色名称、权限配置等必要字段</li>
            <li>3. 导入数据将仅在本地存储，连接网络后将自动同步</li>
            <li>4. 路径不能用中文</li>
          </ul>
        </div>
      </div>
      <span slot="footer" class="dialog-footer">
        <el-button @click="importRoleDialogVisible = false">取 消</el-button>
        <el-button
          type="primary"
          @click="importRole"
          :disabled="!selectedRoleFilePath"
          >导 入</el-button
        >
      </span>
    </el-dialog>

  </div>
</template>

<script>
import Logo from "@/components/Logo.vue";
import UserInfoBar from '@/components/UserInfoBar.vue';
import SoftKeyboard from "@/components/SoftKeyboard.vue";
import BoxStatusDialog from "@/components/BoxStatusDialog.vue";

 import MD5 from 'crypto-js/md5';
export default {
  name: "sysSetting",
  components: {
    Logo,
    UserInfoBar,
    SoftKeyboard,
    BoxStatusDialog,
    
  },
  inject: ["speak", "websocketService", "websocketMsg"],
  watch: {
    websocketMsg: {
      handler(newMessage) {
        console.log("收到新的WebSocket消息系统设置:", newMessage);
        if (newMessage) {
          this.handleSettingMessages(newMessage);
        }
      },
      deep: true,
      immediate: true,
    },
  },
  data() {
    return {
      isLoggedIn: false,
      loginForm: {
        username: "",
        password: "",
      },
      passwordForm: {
        oldPassword: "",
        newPassword: "",
        confirmPassword: "",
      },
      // 密码强度验证
      passwordValidation: {
        length: false,
        uppercase: false,
        lowercase: false,
        number: false,
        special: false,
      },
      serverSettings: {
        serverIp: "",
        serverPort: "",
        offline: 0,
        ssl: 0,
        serverHttpPort: "",
        serverHttps: 0,
      },
      activeTab: 'admin',
      boxList: [],
      selectedBox: null,

      // 分页相关
      currentPage: 1,
      pageSize: 12, // 每页显示12个柜子

      // 长按相关
      longPressTimer: null,
      longPressBox: null,
      longPressProgress: 0,
      isLongPressing: false,
      editBoxData:null,

      // 双击相关
      clickTimer: null,
      clickCount: 0,
      lastClickedBox: null,
      doubleClickDelay: 300, // 双击间隔时间（毫秒）
      longPressDelay: 500, // 长按触发时间（毫秒）
      longPressStartTimer: null, // 长按启动定时器

      // 状态修改对话框
      showStatusDialog: false,
      selectedBoxForEdit: null,

      // 管理员对话框相关
      adminDialogVisible: false,
      adminDialogTitle: "创建管理员",
      adminForm: {
        account: '',
        realName: '',
        mobile: '',
        card: '',
        companyId: '',
        belongDeptId: '',
        workDeptId: '',
        status: '1',
        isFreeAccess: '0',
        roleIds: []
      },
      roleOptions: [],
      organizationtreedata: [],

      // 故障操作标记，用于保持分页状态
      isFromFaultOperation: false,
      currentStateConfig:'',

      // 状态监听器引用
      stateWatcher: null,

      // 管理员抽屉相关
      adminDrawerVisible: false,

      // 升级相关
      upgradeDialogVisible: false,
      selectedUpgradeFilePath: "",

      // 导入组织相关
      importOrgDialogVisible: false,
      selectedOrgFilePath: "",

      // 导入角色相关
      importRoleDialogVisible: false,
      selectedRoleFilePath: "",
    };
  },
  computed: {
    // 计算总页数
    totalPages() {
      return Math.ceil(this.boxList.length / this.pageSize) || 1;
    },
    // 计算当前页显示的柜子列表
    displayedBoxList() {
      const start = (this.currentPage - 1) * this.pageSize;
      const end = start + this.pageSize;
      return this.boxList.slice(start, end);
    },
    // 长按进度圆环相关计算
    progressCircumference() {
      return 2 * Math.PI * 25; // 半径为25的圆周长
    },
    progressOffset() {
      const progress = this.longPressProgress / 100;
      return this.progressCircumference * (1 - progress);
    },
    // 密码强度百分比
    passwordStrengthPercent() {
      const validCount = Object.values(this.passwordValidation).filter(Boolean).length;
      return (validCount / 5) * 100;
    },
    // 密码强度等级
    passwordStrengthClass() {
      const percent = this.passwordStrengthPercent;
      if (percent === 0) return 'strength-none';
      if (percent <= 40) return 'strength-weak';
      if (percent <= 60) return 'strength-medium';
      if (percent <= 80) return 'strength-good';
      return 'strength-strong';
    },
    // 密码强度文本
    passwordStrengthText() {
      const percent = this.passwordStrengthPercent;
      if (percent === 0) return '无';
      if (percent <= 40) return '弱';
      if (percent <= 60) return '中等';
      if (percent <= 80) return '良好';
      return '强';
    },
    // 密码是否符合强度要求
    isPasswordStrong() {
      return Object.values(this.passwordValidation).every(Boolean);
    }
  },
  methods: {
    // 保存页面状态到 sessionStorage
    savePageState() {
      const state = {
        isLoggedIn: this.isLoggedIn,
        activeTab: this.activeTab,
        serverSettings: this.serverSettings,
        currentPage: this.currentPage,
        currentStateConfig: this.currentStateConfig,
        boxList: this.boxList,
        timestamp: Date.now()
      };
      sessionStorage.setItem('sysSettingPageState', JSON.stringify(state));
    },

    // 从 sessionStorage 恢复页面状态
    restorePageState() {
      try {
        const stateStr = sessionStorage.getItem('sysSettingPageState');
        if (stateStr) {
          const state = JSON.parse(stateStr);
          // 检查状态是否过期（30分钟）
          const isExpired = Date.now() - state.timestamp > 30 * 60 * 1000;

          if (!isExpired) {
            this.isLoggedIn = state.isLoggedIn || false;
            this.activeTab = state.activeTab || 'admin';
            this.serverSettings = { ...this.serverSettings, ...state.serverSettings };
            this.currentPage = state.currentPage || 1;
            this.currentStateConfig = state.currentStateConfig !== undefined ? state.currentStateConfig : this.currentStateConfig;
            this.boxList = state.boxList || [];

            console.log('页面状态已恢复:', state);
            return true;
          } else {
            // 状态过期，清除
            sessionStorage.removeItem('sysSettingPageState');
          }
        }
      } catch (error) {
        console.error('恢复页面状态失败:', error);
        sessionStorage.removeItem('sysSettingPageState');
      }
      return false;
    },

    // 清除页面状态
    clearPageState() {
      sessionStorage.removeItem('sysSettingPageState');
    },

    //系统设置消息
    handleSettingMessages(message) {
      if (!message) return;
      // 根据消息类型处理不同的情况
      if (message && message.messages) {
        console.log("系统设置消息:", message, message.messages.type);
        switch (message.messages.type) {
          // case 30005: //柜格关闭响应
          //   if (message.messages.data.code == 1) {
          //     this.speak("柜格已经关闭")
          //     this.getAllGrids();
          //   } else{
          //     this.speak(message.messages.data.msg)
          //   }
          //   break;
            case 30006: //柜格关闭响应
            if (message.messages.data.code == 1) {
              this.speak("柜格已经关闭")
              this.getAllGrids();
            } else{
              this.speak(message.messages.data.msg)
            }
            break; 
          case 30007: //柜格关闭响应
            if (message.messages.data.code == 1) {
              this.speak("柜格已经关闭");
               this.$router.push("/");
              this.getAllGrids();
            } else{
              this.speak(message.messages.data.msg)
            }
            break;
          case 20035: //柜格响应
            if (message.messages.data.code == 1) {
              this.speak("柜格已经打开")
              this.getAllGrids();
            } else {
              this.speak(message.messages.data.msg)
            }
            break;
            case 20034: //柜格响应
            if (message.messages.data.code == 1) {
              this.speak("柜格已经打开");
                     //保持当前分页
              this.isFromFaultOperation = true;
              this.getAllGrids();
            } else {
              this.speak(message.messages.data.msg)
            }
            break;  
          case 20003: //请求获取所有柜子及状态
            if (message.messages.data.code == 1) {

              // console.log("message.messages.data.grids ",message.messages.data.grids)
                   this.boxList = message.messages.data.grids;
                  //  this.boxList =  [{gridCode:1,gridName:'a01',status:1},{gridCode:2,gridName:'a02',status:2},{gridCode:3,gridName:'a03',status:3},{gridCode:4,gridName:'a04',status:4},{gridCode:4,gridName:'a04',status:4},{gridCode:4,gridName:'a04',status:4},{gridCode:4,gridName:'a04',status:4},{gridCode:4,gridName:'a04',status:4},{gridCode:4,gridName:'a04',status:4},{gridCode:4,gridName:'a04',status:4},{gridCode:4,gridName:'a04',status:4},{gridCode:4,gridName:'a04',status:4},{gridCode:4,gridName:'a04',status:4},{gridCode:4,gridName:'a04',status:4},{gridCode:4,gridName:'a04',status:4},{gridCode:4,gridName:'a04',status:4},{gridCode:4,gridName:'a04',status:4}];

               console.log('this.boxList',this.boxList)
              // 只有在不是故障相关操作时才重置到第一页
              if (!this.isFromFaultOperation) {
                this.currentPage = 1;
              }
              // 重置故障操作标记
              this.isFromFaultOperation = false;
            } else {
              this.speak(message.messages.data.msg)
            }
            break;

          case 20030: //获取服务端配置项响应
            if (message.messages.data.code == 1) {
              this.isLoggedIn = true;
              this.getAllGrids();
              this.loadSystemSettings();
            } else {
              this.$message.warning(message.messages.data.msg);
            }
            break;
          case 20031: //获取服务端配置项响应
            this.serverSettings.ssl = message.messages.data.ssl;
            this.serverSettings.offline = message.messages.data.offline;
            this.currentStateConfig = message.messages.data.offline;
            this.serverSettings.serverIp = message.messages.data.serverIp;
            this.serverSettings.serverPort = message.messages.data.serverPort;
            this.serverSettings.serverHttpPort = message.messages.data.serverHttpPort || "";
            this.serverSettings.serverHttps = message.messages.data.serverHttps || 0;
            break;
          case 20032: //设置服务端配置项响应
            if (message.messages.data.code == 1) {
              // this.isLoggedIn = false;
              this.$message.success("设置成功");
               this.loadSystemSettings();
              this.speak("设置成功");
              // this.goHome();
            } else {
              this.$message.warning(message.messages.data.msg);
            }
            break;
            case 30035: //开柜完成通知
            if (message.messages.data.code == 1) {
              this.getAllGrids();
              this.loadSystemSettings();
            } else {
              this.$message.warning(message.messages.data.msg);
            }
            break;   

             case 20107: //解除故障
            if (message.messages.data.code == 1) {
              // 设置故障操作标记，保持当前分页
              this.isFromFaultOperation = true;
              this.getAllGrids();
               this.speak(`格子${this.editBoxData.gridName}故障状态已解除`);
            } else {
              this.$message.warning(message.messages.data.msg);
            }
            break;

             case 20106: //设置故障
            if (message.messages.data.code == 1) {
              // 设置故障操作标记，保持当前分页
              this.isFromFaultOperation = true;
              this.getAllGrids();
              console.log("editBoxData",this.editBoxData);
               this.speak(`格子${this.editBoxData.gridName}状态已修改为故障状态}`);
            } else {
              this.$message.warning(message.messages.data.msg);
            }
            break;

            case 10501: //修改密码响应
            if (message.messages.data.code == 1) {
              this.$message.success("密码修改成功");
              this.resetPasswordForm();
              this.speak("密码修改成功");
            } else {
              this.$message.warning(message.messages.data.msg);
              this.speak(message.messages.data.msg);
            }
            break;

          case 20092: //获取角色列表响应
            if (message.messages.data && message.messages.data.code === 1) {
              this.roleOptions = message.messages.data.list;
            } else {
              this.$message.warning(message.messages.data.msg);
            }
            break;

          case 20080: //获取组织树响应
            if (message.messages.data && message.messages.data.code === 1) {
              this.organizationtreedata = message.messages.data.data;
            } else {
              this.$message.warning(message.messages.data.msg);
            }
            break;

          case 20091: //创建管理员响应
            if (message.messages.data.code == 1) {
              this.$message.success("管理员创建成功");
              this.adminDialogVisible = false;
              this.resetAdminForm();
              this.speak("管理员创建成功");
            } else {
              this.$message.warning(message.messages.data.msg);
              this.speak(message.messages.data.msg);
            }
            break;


          default:
          // console.log("未处理的消息类型:", message);
        }
      }
    },

    goHome() {
      // 清除页面状态，因为返回主页时不需要保持状态
      this.clearPageState();
      this.$router.push("/");
    },
    
    // 切换页面
    changePage(page) {
      if (page >= 1 && page <= this.totalPages) {
        this.currentPage = page;
      }
    },
    
    hanldeOpenAllBox(){
      let order = {
        type: 10035,
        id: "getOpenAllGrids",
        data: {
          // 移除对未定义变量box的引用
        },
      };
      this.websocketService.send(order);
    },
    openBox(box){
      let order = {
        type: 10034,
        id: "openBox",
        data: {
          gridCode:box.gridCode
        },
      };
      this.websocketService.send(order);
    },
    getAllGrids(){
      let order = {
        type: 10003,
        id: "getAllGrids",
        data: {
        
        },
      };
      this.websocketService.send(order);
    },
    loginOrder(params) {
      //请求登录配置用户

      let order = {
        type: 10030,
        id: "loginOrder",
        data: {
          username: params.username,
          //  password: params.password,
           password: MD5(params.password+'beyondinfo').toString(),
        },
      };
      this.websocketService.send(order);
    },

    loadSystemSettings() {
      //请求获取服务端配置项
      let order = {
        type: 10031,
        id: "loadSystemSettings",
      };
      this.websocketService.send(order);
    },

    stopMonitoring(){
       let order = {
        type: 10400,
        id: "stopMonitoring",
      };
      this.websocketService.send(order);
    },

    saveSystemSettings() {
      //请求保存服务端配置项
      let order = {
        type: 10032,
        id: "saveSystemSettings",
        data: {
          serverIp: this.serverSettings.serverIp,
          serverPort: +this.serverSettings.serverPort,
          offline: +this.serverSettings.offline,
          ssl: +this.serverSettings.ssl,
          serverHttpPort: +this.serverSettings.serverHttpPort,
          serverHttps: +this.serverSettings.serverHttps,
        },
      };
      this.websocketService.send(order);
    },

    handleLogin() {
      // 登录验证逻辑
      if (!this.loginForm.username || !this.loginForm.password) {
        this.$message.warning("请输入管理员账号和管理员密码");
        return;
      }

      this.loginOrder(this.loginForm);
    },

    resetSettings() {
      // 重置所有设置
      this.serverSettings = {
        serverIp: "",
        serverPort: "",
        offline: 0,
        ssl: 0,
        serverHttpPort: "",
        serverHttps: 0,
      };
    },

    saveSettings() {
      // 保存设置
      if (this.serverSettings.offline === 1) {
        // 验证在线模式必填字段
        if (!this.serverSettings.serverIp || !this.serverSettings.serverPort || !this.serverSettings.serverHttpPort) {
          this.$message.warning("请填写服务器IP、端口和HTTP端口");
          return;
        }
      }
      this.saveSystemSettings();
    },

    // 重置密码表单
    resetPasswordForm() {
      this.passwordForm = {
        oldPassword: "",
        newPassword: "",
        confirmPassword: "",
      };
      // 重置密码验证状态
      this.passwordValidation = {
        length: false,
        uppercase: false,
        lowercase: false,
        number: false,
        special: false,
      };
    },

    // 验证密码强度
    validatePasswordStrength() {
      const password = this.passwordForm.newPassword;

      // 长度验证（至少8位）
      this.passwordValidation.length = password.length >= 8;

      // 大写字母验证
      this.passwordValidation.uppercase = /[A-Z]/.test(password);

      // 小写字母验证
      this.passwordValidation.lowercase = /[a-z]/.test(password);

      // 数字验证
      this.passwordValidation.number = /[0-9]/.test(password);

      // 特殊符号验证
      this.passwordValidation.special = /[!@#$%^&*()_+\-=[\]{}|;:,.<>?]/.test(password);
    },

    // 处理修改密码
    handleChangePassword() {
      // 验证表单
      if (!this.passwordForm.oldPassword || !this.passwordForm.newPassword || !this.passwordForm.confirmPassword) {
        this.$message.warning("请填写所有密码字段");
        return;
      }

      // 验证新密码和确认密码是否一致
      if (this.passwordForm.newPassword !== this.passwordForm.confirmPassword) {
        this.$message.warning("新密码和确认密码不一致");
        return;
      }

      // 验证新密码不能与原密码相同
      if (this.passwordForm.oldPassword === this.passwordForm.newPassword) {
        this.$message.warning("新密码不能与原密码相同");
        return;
      }

      // 验证密码强度
      if (!this.isPasswordStrong) {
        this.$message.warning("新密码不符合强度要求，请确保密码长度至少8位，包含大小写字母、数字和特殊符号");
        return;
      }

      // 发送修改密码请求
      this.changePasswordOrder();
    },

    // 发送修改密码请求
    changePasswordOrder() {
      let order = {
        type: 10501,
        id: "changePassword",
        data: {
          // oldPassword: this.passwordForm.oldPassword,
          // newPassword: this.passwordForm.newPassword,
           oldPassword: MD5(this.passwordForm.oldPassword+'beyondinfo').toString(),
          newPassword: MD5(this.passwordForm.newPassword+'beyondinfo').toString(),
          
        },
      };
      this.websocketService.send(order);
    },

    handleBoxClick(box) {
      // 如果正在长按，忽略点击事件
      if (this.isLongPressing) {
        return;
      }

      // 双击检测逻辑
      if (this.lastClickedBox && this.lastClickedBox.gridCode === box.gridCode) {
        // 同一个柜子的第二次点击
        this.clickCount++;
        if (this.clickCount === 2) {
          // 双击确认，执行开柜操作
          this.clearClickTimer();
          this.executeOpenBox(box);
          return;
        }
      } else {
        // 不同柜子或第一次点击
        this.clickCount = 1;
        this.lastClickedBox = box;
      }

      // 设置双击检测定时器
      this.clearClickTimer();
      this.clickTimer = setTimeout(() => {
        // 超时后重置点击状态（单击不执行任何操作）
        this.resetClickState();
      }, this.doubleClickDelay);
    },

    // 执行开柜操作
    executeOpenBox(box) {
      // 双击确认时，立即取消任何长按相关操作
      this.cancelLongPressStart();
      this.cancelLongPress();

      this.selectedBox = box;
      this.openBox(box);
      this.resetClickState();
    },

    // 清除点击定时器
    clearClickTimer() {
      if (this.clickTimer) {
        clearTimeout(this.clickTimer);
        this.clickTimer = null;
      }
    },

    // 重置点击状态
    resetClickState() {
      this.clickCount = 0;
      this.lastClickedBox = null;
      this.clearClickTimer();
      this.cancelLongPressStart(); // 同时取消长按启动
    },

    // 取消长按启动定时器
    cancelLongPressStart() {
      if (this.longPressStartTimer) {
        clearTimeout(this.longPressStartTimer);
        this.longPressStartTimer = null;
      }
    },

    // 鼠标按下事件处理 - 只用于长按检测
    handleMouseDown(box) {
      // 清除之前的长按启动定时器
      this.cancelLongPressStart();

      // 延迟启动长按，避免与双击冲突
      this.longPressStartTimer = setTimeout(() => {
        // 只有在没有进行双击操作时才启动长按
        if (this.clickCount <= 1) {
          this.startLongPress(box);
        }
      }, this.longPressDelay);
    },

    // 鼠标抬起事件处理
    handleMouseUp() {
      this.cancelLongPressStart();
      this.cancelLongPress();
    },

    // 鼠标离开事件处理
    handleMouseLeave() {
      this.cancelLongPressStart();
      this.cancelLongPress();
    },

    // 触摸开始事件处理
    handleTouchStart(box) {
      // 清除之前的长按启动定时器
      this.cancelLongPressStart();

      // 延迟启动长按，避免与双击冲突
      this.longPressStartTimer = setTimeout(() => {
        // 只有在没有进行双击操作时才启动长按
        if (this.clickCount <= 1) {
          this.startLongPress(box);
        }
      }, this.longPressDelay);
    },

    // 触摸结束事件处理
    handleTouchEnd() {
      this.cancelLongPressStart();
      this.cancelLongPress();
    },

    // 触摸取消事件处理
    handleTouchCancel() {
      this.cancelLongPressStart();
      this.cancelLongPress();
    },

    // 开始长按
    startLongPress(box) {
      // 如果已经双击了，不启动长按
      if (this.clickCount >= 2) {
        return;
      }

      // 清除之前的计时器
      this.cancelLongPress();

      this.longPressBox = box;
      this.longPressProgress = 0;
      this.isLongPressing = true; // 标记正在长按

      // 清除双击状态，因为开始长按了
      this.resetClickState();

      // 创建长按计时器
      this.longPressTimer = setInterval(() => {
        this.longPressProgress += 2; // 每20ms增加2%

        if (this.longPressProgress >= 100) {
          // 长按完成
          this.completeLongPress();
        }
      }, 20);
    },

    // 取消长按
    cancelLongPress() {
      // 清除长按计时器
      if (this.longPressTimer) {
        clearInterval(this.longPressTimer);
        this.longPressTimer = null;
      }

      // 立即重置状态
      this.longPressBox = null;
      this.longPressProgress = 0;
      this.isLongPressing = false;
    },

    // 完成长按
    completeLongPress() {
      // 保存当前长按的格子信息
      const currentBox = { ...this.longPressBox };

      // 取消长按状态
      this.cancelLongPress();

      // 显示状态修改对话框
      this.selectedBoxForEdit = currentBox;
      this.showStatusDialog = true;
    },

    getStatusText(status) {
      switch (status) {
        case 1:
          return "空置中";
        case 2:
          return "占用中";
        case 3:
          return "开启中";
        case 4:
          return "故障中";
        default:
          return "未知状态";
      }
    },

    refreshBoxStatus() {
      // 实现刷新状态的逻辑
      console.log("刷新状态");
    },

    openSelectedBox() {
      if (this.selectedBox) {
        // 实现开启选中柜门的逻辑
        console.log("开启柜门:", this.selectedBox);
      } else {
        this.$message.warning("请选择要开启的柜门");
      }
    },

    // 取消状态编辑
    cancelStatusEdit() {
      this.showStatusDialog = false;
      this.selectedBoxForEdit = null;
    },

    // 确认状态编辑
    confirmStatusEdit(data) {

      console.log("data111",data);
      const { boxData, newStatus } = data;
      this.editBoxData = boxData;


      // 发送状态修改请求
      this.updateBoxStatus(boxData, newStatus);

      // 关闭对话框
      this.showStatusDialog = false;
      this.selectedBoxForEdit = null;
    },

    // 更新柜子状态
    updateBoxStatus(box, newStatus) {
      // 发送WebSocket消息更新柜子状态

      let order = {}
      if (newStatus === 4) {
        // 如果是设置为故障状态，发送故障请求
         order = {
        type: 10106,
        id: "updateBoxStatusFalt",
        data: {
          gridCode: box.gridCode,
        },
      };
      }else{
            order = {
        type: 10107,
        id: "updateBoxStatusNormal",
        data: {
          gridCode: box.gridCode,
        },
      };
      }
      console.log("发送状态更新请求:", order);
      this.websocketService.send(order);
    },

    // 创建管理员
    handleCreateAdmin() {
      // 保存当前页面状态
      this.savePageState();

      // 跳转到用户管理页面，并传递 roleType 参数为 1
      this.$router.push({
        name: "userManage",
        query: {
          roleType: 1 // 查询角色类型为管理员的用户
        }
      });
    },

    // 获取角色选项
    getRoleOptions() {
      const order = {
        type: 10092,
        id: "getRoleOptions",
      };
      this.websocketService.send(order);
    },

    // 获取组织树数据
    getOrganizationtreedata() {
      const order = {
        type: 10080,
        id: "getOrganizationtreedata",
        data: { parentId: 0 },
      };
      this.websocketService.send(order);
    },

    // 重置管理员表单
    resetAdminForm() {
      this.adminForm = {
        account: '',
        realName: '',
        mobile: '',
        card: '',
        companyId: '',
        belongDeptId: '',
        workDeptId: '',
        status: '1',
        gender: "1",
        roleIds: []
      };
    },

    // 处理管理员对话框提交
    onAdminDialogSubmit(formData) {
      const order = {
        type: 10091,
        id: "addAdmin",
        data: formData,
      };
      this.websocketService.send(order);
    },

    // 打开管理员抽屉
    openAdminDrawer() {
      this.adminDrawerVisible = true;
    },

    // 关闭抽屉前的处理
    handleDrawerClose(done) {
      this.adminDrawerVisible = false;
      done();
    },

    // 管理员列表
    handleManageAdmin() {
      this.adminDrawerVisible = false;
      // 跳转到用户管理页面，显示管理员列表
      this.$router.push({
        name: "userManage",
        query: {
          roleType: 1 // 查询角色类型为管理员的用户
        }
      });
    },

    // 权限设置
    handleAdminPermissions() {
      this.adminDrawerVisible = false;
      this.$message.info("权限设置功能开发中...");
    },

    // 操作日志
    handleAdminLogs() {
      this.adminDrawerVisible = false;
      this.$message.info("操作日志功能开发中...");
    },

    // 系统升级
    handleSystemUpgrade() {
      this.adminDrawerVisible = false;
      this.upgradeDialogVisible = true;
      this.selectedUpgradeFilePath = "";
    },

    // 选择升级文件
    selectUpgradeFile() {
      if (window.electronAPI) {
        window.electronAPI
          .selectImportFile({
            title: "选择升级包文件",
            filters: [
              { name: "压缩文件", extensions: ["zip", "rar", "7z", "tar", "gz"] },
            ],
            properties: ["openFile"],
          })
          .then((result) => {
            if (
              !result.canceled &&
              result.filePaths &&
              result.filePaths.length > 0
            ) {
              this.selectedUpgradeFilePath = result.filePaths[0];
              console.log("选择的升级包路径:", this.selectedUpgradeFilePath);
            }
          })
          .catch((err) => {
            console.error("选择文件出错:", err);
            this.$message.error("选择文件失败，请重试");
          });
      } else {
        console.error("无法访问Electron API");
        this.$message.error("当前环境不支持文件选择功能");
      }
    },

    // 执行升级
    performUpgrade() {
      if (!this.selectedUpgradeFilePath) {
        this.$message.warning("请先选择要升级的文件");
        return;
      }

      this.$loading({
        lock: true,
        text: "正在执行系统升级，请稍候...",
        spinner: "el-icon-loading",
        background: "rgba(0, 0, 0, 0.7)",
      });

      // 发送升级请求到后端
      let order = {
        type: 10098, // 假设升级的消息类型
        id: "systemUpgrade",
        data: {
          path: this.selectedUpgradeFilePath,
        },
      };

      this.websocketService.send(order);
      this.upgradeDialogVisible = false;
    },

    // 导入组织
    handleImportOrganization() {
      this.adminDrawerVisible = false;
      this.importOrgDialogVisible = true;
      this.selectedOrgFilePath = "";
    },

    // 选择组织文件
    selectOrgFile() {
      if (window.electronAPI) {
        window.electronAPI
          .selectImportFile({
            title: "选择组织数据文件",
            filters: [
              { name: "Excel文件", extensions: ["xlsx", "xls"] },
              { name: "CSV文件", extensions: ["csv"] },
            ],
            properties: ["openFile"],
          })
          .then((result) => {
            if (
              !result.canceled &&
              result.filePaths &&
              result.filePaths.length > 0
            ) {
              this.selectedOrgFilePath = result.filePaths[0];
              console.log("选择的组织文件路径:", this.selectedOrgFilePath);
            }
          })
          .catch((err) => {
            console.error("选择文件出错:", err);
            this.$message.error("选择文件失败，请重试");
          });
      } else {
        console.error("无法访问Electron API");
        this.$message.error("当前环境不支持文件选择功能");
      }
    },

    // 导入组织数据
    importOrganization() {
      if (!this.selectedOrgFilePath) {
        this.$message.warning("请先选择要导入的文件");
        return;
      }

      this.$loading({
        lock: true,
        text: "正在导入组织数据，请稍候...",
        spinner: "el-icon-loading",
        background: "rgba(0, 0, 0, 0.7)",
      });

      // 发送导入请求到后端
      let order = {
        type: 10099, // 假设导入组织的消息类型
        id: "importOrganization",
        data: {
          path: this.selectedOrgFilePath,
        },
      };

      this.websocketService.send(order);
      this.importOrgDialogVisible = false;
    },

    // 导入角色
    handleImportRole() {
      this.adminDrawerVisible = false;
      this.importRoleDialogVisible = true;
      this.selectedRoleFilePath = "";
    },

    // 选择角色文件
    selectRoleFile() {
      if (window.electronAPI) {
        window.electronAPI
          .selectImportFile({
            title: "选择角色数据文件",
            filters: [
              { name: "Excel文件", extensions: ["xlsx", "xls"] },
              { name: "CSV文件", extensions: ["csv"] },
            ],
            properties: ["openFile"],
          })
          .then((result) => {
            if (
              !result.canceled &&
              result.filePaths &&
              result.filePaths.length > 0
            ) {
              this.selectedRoleFilePath = result.filePaths[0];
              console.log("选择的角色文件路径:", this.selectedRoleFilePath);
            }
          })
          .catch((err) => {
            console.error("选择文件出错:", err);
            this.$message.error("选择文件失败，请重试");
          });
      } else {
        console.error("无法访问Electron API");
        this.$message.error("当前环境不支持文件选择功能");
      }
    },

    // 导入角色数据
    importRole() {
      if (!this.selectedRoleFilePath) {
        this.$message.warning("请先选择要导入的文件");
        return;
      }

      this.$loading({
        lock: true,
        text: "正在导入角色数据，请稍候...",
        spinner: "el-icon-loading",
        background: "rgba(0, 0, 0, 0.7)",
      });

      // 发送导入请求到后端
      let order = {
        type: 10100, // 假设导入角色的消息类型
        id: "importRole",
        data: {
          path: this.selectedRoleFilePath,
        },
      };

      this.websocketService.send(order);
      this.importRoleDialogVisible = false;
    },
  },

  // 组件创建时恢复状态
  created() {
    // 尝试恢复页面状态
    const stateRestored = this.restorePageState();

    // 如果状态恢复成功且已登录，则不需要重新登录
    if (stateRestored && this.isLoggedIn) {
      console.log('页面状态已恢复，跳过登录流程');
      // 如果有柜子数据，不需要重新获取
      if (this.boxList.length === 0) {
        this.getAllGrids();
      }
      // 如果服务器设置为空，重新加载
      if (!this.serverSettings.serverIp && !this.serverSettings.serverPort) {
        this.loadSystemSettings();
      }
    }
  },

  // 组件挂载时的处理
  mounted() {
    // 监听页面状态变化，定期保存状态
    this.stateWatcher = this.$watch(
      function() {
        return {
          isLoggedIn: this.isLoggedIn,
          activeTab: this.activeTab,
          serverSettings: this.serverSettings,
          currentPage: this.currentPage,
          currentStateConfig: this.currentStateConfig,
          boxList: this.boxList
        };
      },
      function() {
        // 如果已登录，保存状态
        if (this.isLoggedIn) {
          this.savePageState();
        }
      },
      { deep: true }
    );
  },

  // 组件销毁时清理定时器和状态监听器
  beforeDestroy() {
    this.stopMonitoring();
    this.cancelLongPress();
    this.cancelLongPressStart();
    this.clearClickTimer();

    // 清理状态监听器
    if (this.stateWatcher) {
      this.stateWatcher();
    }
  },
};
</script>

<style scoped>
/* 科技蓝色主题颜色 */
/* 主色调 */
@keyframes glow {
  0% {
    box-shadow: 0 0 5px #0066ff;
  }
  50% {
    box-shadow: 0 0 20px #0066ff;
  }
  100% {
    box-shadow: 0 0 5px #0066ff;
  }
}

.tech-card {
  background: linear-gradient(
    135deg,
    rgba(0, 102, 255, 0.1),
    rgba(0, 163, 255, 0.1)
  );
  border: 2px solid;
  border-image-slice: 1;
  border-image-source: linear-gradient(45deg, #0066ff, #00a3ff);
  transition: all 0.3s ease;
  border-radius: 0.5rem;
}

.tech-card:hover {
  animation: glow 2s infinite;
  transform: scale(1.02);
}

.tech-grid {
  background-image: linear-gradient(rgba(0, 102, 255, 0.1) 1px, transparent 1px),
    linear-gradient(90deg, rgba(0, 102, 255, 0.1) 1px, transparent 1px);
  background-size: 20px 20px;
}

.tech-input {
  width: 100%;
  padding: 10px;
  background-color: rgba(0, 0, 0, 0.3);
  border: 1px solid;
  border-image-slice: 1;
  border-image-source: linear-gradient(45deg, #0066ff, #00a3ff);
  color: white;
  border-radius: 4px;
  transition: all 0.3s ease;
}

.tech-input:focus {
  border-color: #0066ff;
  animation: glow 2s infinite;
  outline: none;
}

.form-group {
  margin-bottom: 20px;
}

.form-group label {
  display: block;
  margin-bottom: 8px;
  color: #e2e8f0;
  font-weight: 500;
}

.required {
  color: #ff4f4f;
}

.tech-radio-container {
  display: flex;
  gap: 24px;
  margin-top: 10px;
}

.radio-option {
  position: relative;
  display: flex;
  align-items: center;
  cursor: pointer;
  user-select: none;
  color: #a0aec0;
}

.radio-option:hover {
  color: #0066ff;
}

.radio-option input[type="radio"] {
  position: absolute;
  opacity: 0;
  cursor: pointer;
  height: 0;
  width: 0;
}

.radio-circle {
  position: relative;
  height: 18px;
  width: 18px;
  background-color: transparent;
  border: 2px solid #a0aec0;
  border-radius: 50%;
  display: inline-block;
  margin-right: 8px;
}

.radio-option:hover .radio-circle {
  border-color: #0066ff;
}

.radio-option input:checked ~ .radio-circle {
  border-color: #0066ff;
}

.radio-option input:checked ~ .radio-circle:after {
  content: "";
  position: absolute;
  display: block;
  top: 2px;
  left: 2px;
  width: 10px;
  height: 10px;
  border-radius: 50%;
  background: #0066ff;
}

/* 选中状态下的文本颜色 */
.radio-option input:checked ~ .radio-text {
  color: #0066ff;
  font-weight: 500;
}

.radio-text {
  font-size: 14px;
  line-height: 18px;
}

@keyframes pulse {
  0% {
    box-shadow: 0 0 0 0 rgba(0, 102, 255, 0.4);
  }
  70% {
    box-shadow: 0 0 0 5px rgba(0, 102, 255, 0);
  }
  100% {
    box-shadow: 0 0 0 0 rgba(0, 102, 255, 0);
  }
}

.form-actions {
  display: flex;
  justify-content: center;
  gap: 15px;
  margin-top: 30px;
}

.tech-button {
  padding: 10px 25px;
  background: linear-gradient(45deg, #0066ff, #00a3ff);
  color: white;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  position: relative;
  overflow: hidden;
  transition: all 0.3s ease;
  font-weight: 500;
  text-transform: uppercase;
  letter-spacing: 1px;
}

.tech-button.small {
  padding: 8px 15px;
  font-size: 12px;
}

.tech-button::before {
  content: "";
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(
    90deg,
    transparent,
    rgba(255, 255, 255, 0.3),
    transparent
  );
  transition: all 0.5s ease;
}

.tech-button:hover::before {
  left: 100%;
}

.tech-button.outline {
  background: transparent;
  border: 1px solid #0066ff;
  color: #0066ff;
}

/* 禁用输入框的上下箭头 */
input::-webkit-outer-spin-button,
input::-webkit-inner-spin-button {
  -webkit-appearance: none;
  margin: 0;
}

/* Tab选项卡样式 */
.tech-tabs {
  display: flex;
  border-bottom: 2px solid rgba(0, 102, 255, 0.3);
  margin-bottom: 20px;
}

.tech-tab {
  padding: 12px 24px;
  cursor: pointer;
  color: #a0aec0;
  position: relative;
  transition: all 0.3s ease;
  text-align: center;
  font-weight: 500;
  flex: 1;
}

.tech-tab:hover {
  color: #0066ff;
}

.tech-tab.active {
  color: #0066ff;
}

.tech-tab.active::after {
  content: '';
  position: absolute;
  bottom: -2px;
  left: 0;
  width: 100%;
  height: 2px;
  background: linear-gradient(45deg, #0066ff, #00a3ff);
  animation: glow 2s infinite;
}

/* 柜子格子布局样式 */
.grid-container {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  grid-template-rows: repeat(4, 1fr);
  gap: 15px;
  margin-bottom: 20px;
}

.grid-box {
  position: relative;
  height: 80px;
  border: 2px solid;
  border-radius: 8px;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.3s ease;
  user-select: none;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
}

.grid-box:hover {
  transform: scale(1.05);
}

.grid-box.long-pressing {
  transform: scale(1.1);
  z-index: 10;
}

.grid-box.vacant {
  border-color: #22c55e;
  background-color: rgba(34, 197, 94, 0.1);
}

.grid-box.occupying {
  border-color: #ef4444;
  background-color: rgba(239, 68, 68, 0.1);
}

.grid-box.opening {
  border-color: #eab308;
  background-color: rgba(234, 179, 8, 0.1);
  animation: blink 1s infinite;
}

.grid-box.fault {
  border-color: #9333ea;
  background-color: rgba(147, 51, 234, 0.1);
  animation: pulse 2s infinite;
}

@keyframes blink {
  0% { opacity: 0.7; }
  50% { opacity: 1; }
  100% { opacity: 0.7; }
}

.box-number {
  font-size: 18px;
  font-weight: bold;
  color: #e2e8f0;
}

.box-status {
  font-size: 12px;
  margin-top: 5px;
}

.grid-box.vacant .box-status {
  color: #22c55e;
}

.grid-box.occupying .box-status {
  color: #ef4444;
}

.grid-box.opening .box-status {
  color: #eab308;
}

.grid-box.fault .box-status {
  color: #9333ea;
}

/* 分页控制器样式 */
.pagination-container {
  display: flex;
  justify-content: center;
  align-items: center;
  margin-top: 20px;
}

.pagination-btn {
  display: flex;
  justify-content: center;
  align-items: center;
  width: 40px;
  height: 40px;
  background: rgba(0, 102, 255, 0.1);
  border: 1px solid;
  border-image-slice: 1;
  border-image-source: linear-gradient(45deg, #0066ff, #00a3ff);
  border-radius: 50%;
  color: #0066ff;
  cursor: pointer;
  transition: all 0.3s ease;
}

.pagination-btn:hover:not(:disabled) {
  background: rgba(0, 102, 255, 0.2);
  transform: scale(1.1);
}

.pagination-btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.pagination-text {
  margin: 0 15px;
  color: #e2e8f0;
  font-size: 16px;
  font-weight: 500;
}

/* 长按指示器样式 */
.long-press-indicator {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  z-index: 20;
}

.progress-ring {
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
}

.progress-svg {
  transform: rotate(-90deg);
}

.progress-circle {
  transition: stroke-dashoffset 0.02s linear;
}

.progress-icon {
  position: absolute;
  color: #0066ff;
  font-size: 18px;
  animation: pulse 1s infinite;
}

@keyframes pulse {
  0% {
    transform: scale(1);
    opacity: 0.8;
  }
  50% {
    transform: scale(1.1);
    opacity: 1;
  }
  100% {
    transform: scale(1);
    opacity: 0.8;
  }
}

/* 密码强度验证样式 */
.password-requirements {
  margin-top: 10px;
  padding: 12px;
  background: rgba(0, 102, 255, 0.05);
  border: 1px solid rgba(0, 102, 255, 0.2);
  border-radius: 6px;
}

.requirement-title {
  color: #e2e8f0;
  font-size: 13px;
  font-weight: 500;
  margin-bottom: 8px;
}

.requirement-list {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.requirement-item {
  display: flex;
  align-items: center;
  font-size: 12px;
  color: #ef4444;
  transition: color 0.3s ease;
}

.requirement-item.valid {
  color: #22c55e;
}

.requirement-item i {
  width: 14px;
  margin-right: 6px;
  font-size: 10px;
}

.password-strength-indicator {
  margin-top: 12px;
  padding-top: 10px;
  border-top: 1px solid rgba(0, 102, 255, 0.1);
}

.strength-label {
  color: #e2e8f0;
  font-size: 12px;
  margin-bottom: 6px;
}

.strength-bar {
  width: 100%;
  height: 6px;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 3px;
  overflow: hidden;
  margin-bottom: 4px;
}

.strength-fill {
  height: 100%;
  transition: width 0.3s ease, background-color 0.3s ease;
  border-radius: 3px;
}

.strength-text {
  font-size: 11px;
  font-weight: 500;
  text-align: right;
}

/* 密码强度颜色 */
.strength-none .strength-fill,
.strength-none {
  background-color: #6b7280;
  color: #6b7280;
}

.strength-weak .strength-fill,
.strength-weak {
  background-color: #ef4444;
  color: #ef4444;
}

.strength-medium .strength-fill,
.strength-medium {
  background-color: #f59e0b;
  color: #f59e0b;
}

.strength-good .strength-fill,
.strength-good {
  background-color: #3b82f6;
  color: #3b82f6;
}

.strength-strong .strength-fill,
.strength-strong {
  background-color: #22c55e;
  color: #22c55e;
}

/* 管理员抽屉样式 */
:deep(.admin-drawer) {
  background-color: #1a1a2e !important;
}

:deep(.admin-drawer .el-drawer__header) {
  background: linear-gradient(45deg, #0066ff, #00a3ff);
  color: white;
  padding: 20px;
  margin-bottom: 0;
}

:deep(.admin-drawer .el-drawer__title) {
  color: white !important;
  font-weight: bold;
  font-size: 18px;
}

:deep(.admin-drawer .el-drawer__close-btn) {
  color: white !important;
  font-size: 20px;
}

:deep(.admin-drawer .el-drawer__close-btn:hover) {
  color: #e2e8f0 !important;
}

:deep(.admin-drawer .el-drawer__body) {
  padding: 0;
  background-color: #1a1a2e;
}

.drawer-content {
  padding: 20px;
  height: 100%;
  background-color: #1a1a2e;
}

.drawer-title {
  color: #e2e8f0;
  font-size: 16px;
  font-weight: 600;
  margin-bottom: 20px;
  padding-bottom: 10px;
  border-bottom: 1px solid rgba(0, 102, 255, 0.3);
}

.drawer-menu {
  display: flex;
  flex-direction: column;
  gap: 10px;
}

.menu-item {
  display: flex;
  align-items: center;
  padding: 15px 20px;
  background: rgba(0, 102, 255, 0.1);
  border: 1px solid rgba(0, 102, 255, 0.3);
  border-radius: 8px;
  color: #e2e8f0;
  cursor: pointer;
  transition: all 0.3s ease;
  user-select: none;
}

.menu-item:hover {
  background: rgba(0, 102, 255, 0.2);
  border-color: #0066ff;
  transform: translateX(5px);
  box-shadow: 0 4px 12px rgba(0, 102, 255, 0.3);
}

.menu-item i {
  font-size: 18px;
  margin-right: 12px;
  color: #0066ff;
  width: 20px;
  text-align: center;
}

.menu-item span {
  font-size: 14px;
  font-weight: 500;
}

/* 响应式设计 - 适配各种分辨率 */
/* 超大屏幕 (1920px+) */
@media (min-width: 1920px) {
  .tech-card {
    max-width: 600px;
    margin: 0 auto;
  }

  .tech-input {
    padding: 12px;
    font-size: 16px;
  }

  .tech-button {
    padding: 12px 30px;
    font-size: 16px;
  }

  .grid-container {
    gap: 20px;
  }

  .grid-box {
    height: 100px;
  }

  .box-number {
    font-size: 20px;
  }

  .box-status {
    font-size: 14px;
  }
}

/* 大屏幕 (1440px - 1919px) */
@media (min-width: 1440px) and (max-width: 1919px) {
  .tech-card {
    max-width: 550px;
    margin: 0 auto;
  }

  .tech-input {
    padding: 11px;
    font-size: 15px;
  }

  .tech-button {
    padding: 11px 28px;
    font-size: 15px;
  }

  .grid-container {
    gap: 18px;
  }

  .grid-box {
    height: 90px;
  }

  .box-number {
    font-size: 19px;
  }

  .box-status {
    font-size: 13px;
  }
}

/* 中等屏幕 (1024px - 1439px) */
@media (min-width: 1024px) and (max-width: 1439px) {
  .tech-card {
    max-width: 500px;
    margin: 0 auto;
  }

  .tech-input {
    padding: 10px;
    font-size: 14px;
  }

  .tech-button {
    padding: 10px 25px;
    font-size: 14px;
  }

  .grid-container {
    gap: 15px;
  }

  .grid-box {
    height: 80px;
  }

  .box-number {
    font-size: 18px;
  }

  .box-status {
    font-size: 12px;
  }
}

/* 小屏幕 (768px - 1023px) */
@media (min-width: 768px) and (max-width: 1023px) {
  .container {
    padding: 0 20px;
  }

  .tech-card {
    max-width: 450px;
    margin: 0 auto;
    padding: 20px !important;
  }

  .tech-input {
    padding: 9px;
    font-size: 14px;
  }

  .tech-button {
    padding: 9px 20px;
    font-size: 13px;
  }

  .tech-tabs {
    flex-direction: column;
  }

  .tech-tab {
    padding: 10px 20px;
    border-bottom: 1px solid rgba(0, 102, 255, 0.2);
  }

  .tech-tab.active::after {
    bottom: 0;
    height: 1px;
  }

  .grid-container {
    grid-template-columns: repeat(2, 1fr);
    grid-template-rows: repeat(6, 1fr);
    gap: 12px;
  }

  .grid-box {
    height: 70px;
  }

  .box-number {
    font-size: 16px;
  }

  .box-status {
    font-size: 11px;
  }

  .form-actions {
    flex-direction: column;
    gap: 10px;
  }

  .tech-button {
    width: 100%;
  }

  .password-requirements {
    padding: 10px;
  }

  .requirement-title {
    font-size: 12px;
  }

  .requirement-item {
    font-size: 11px;
  }

  .strength-label {
    font-size: 11px;
  }

  .strength-text {
    font-size: 10px;
  }
}

/* 超小屏幕 (480px - 767px) */
@media (min-width: 480px) and (max-width: 767px) {
  .container {
    padding: 0 15px;
  }

  .tech-card {
    max-width: 100%;
    margin: 0;
    padding: 15px !important;
  }

  .tech-input {
    padding: 8px;
    font-size: 13px;
  }

  .tech-button {
    padding: 8px 15px;
    font-size: 12px;
  }

  .tech-tabs {
    flex-direction: column;
  }

  .tech-tab {
    padding: 8px 15px;
    font-size: 13px;
  }

  .grid-container {
    grid-template-columns: repeat(2, 1fr);
    grid-template-rows: repeat(6, 1fr);
    gap: 10px;
  }

  .grid-box {
    height: 60px;
  }

  .box-number {
    font-size: 14px;
  }

  .box-status {
    font-size: 10px;
  }

  .form-group label {
    font-size: 13px;
  }

  .pagination-text {
    font-size: 14px;
  }

  .pagination-btn {
    width: 35px;
    height: 35px;
  }

  .password-requirements {
    padding: 8px;
  }

  .requirement-title {
    font-size: 11px;
  }

  .requirement-item {
    font-size: 10px;
  }

  .strength-label {
    font-size: 10px;
  }

  .strength-text {
    font-size: 9px;
  }
}

/* 极小屏幕 (320px - 479px) */
@media (max-width: 479px) {
  .container {
    padding: 0 10px;
  }

  .tech-card {
    max-width: 100%;
    margin: 0;
    padding: 12px !important;
  }

  .tech-input {
    padding: 7px;
    font-size: 12px;
  }

  .tech-button {
    padding: 7px 12px;
    font-size: 11px;
  }

  .tech-tabs {
    flex-direction: column;
  }

  .tech-tab {
    padding: 6px 12px;
    font-size: 12px;
  }

  .grid-container {
    grid-template-columns: repeat(1, 1fr);
    grid-template-rows: repeat(12, 1fr);
    gap: 8px;
  }

  .grid-box {
    height: 50px;
  }

  .box-number {
    font-size: 12px;
  }

  .box-status {
    font-size: 9px;
  }

  .form-group {
    margin-bottom: 15px;
  }

  .form-group label {
    font-size: 12px;
    margin-bottom: 5px;
  }

  .pagination-text {
    font-size: 12px;
  }

  .pagination-btn {
    width: 30px;
    height: 30px;
  }

  h2 {
    font-size: 16px !important;
  }

  .password-requirements {
    padding: 6px;
  }

  .requirement-title {
    font-size: 10px;
  }

  .requirement-item {
    font-size: 9px;
  }

  .requirement-item i {
    width: 12px;
    font-size: 8px;
  }

  .strength-label {
    font-size: 9px;
  }

  .strength-text {
    font-size: 8px;
  }

  .strength-bar {
    height: 4px;
  }
}

/* 导入对话框样式 */
:deep(.import-dialog) {
  border-radius: 4px !important;
  overflow: hidden !important;
}

:deep(.import-dialog .el-dialog) {
  background-color: #1c1f37 !important;
  border: 1px solid #263566 !important;
  border-radius: 4px !important;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.5) !important;
  overflow: hidden !important;
}

:deep(.import-dialog .el-dialog__header) {
  background-color: #263566 !important;
  padding: 15px 20px !important;
  border-bottom: 1px solid #263566 !important;
}

:deep(.import-dialog .el-dialog__title) {
  color: #fff !important;
  font-weight: bold !important;
}

:deep(.import-dialog .el-dialog__body) {
  color: #fff !important;
  padding: 0 !important;
}

:deep(.import-dialog .el-dialog__footer) {
  border-top: 1px solid #263566 !important;
  padding: 15px 20px !important;
}

.import-container {
  padding: 20px;
}

.import-tips {
  background-color: rgba(255, 229, 100, 0.1);
  border-left: 4px solid #e6a23c;
  padding: 10px 15px;
  border-radius: 0 4px 4px 0;
  color: #fff;
}

.text-warning {
  color: #e6a23c;
  font-weight: bold;
}

.import-select-btn {
  width: 180px;
  background-color: #3f68cd !important;
  border-color: #3f68cd !important;
}

.selected-file-path {
  color: #67c23a;
  word-break: break-all;
}

.text-truncate {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.text-center {
  text-align: center;
}

.mb-4 {
  margin-bottom: 1rem;
}

.mt-3 {
  margin-top: 0.75rem;
}

.mt-4 {
  margin-top: 1rem;
}

.mb-2 {
  margin-bottom: 0.5rem;
}

.ml-4 {
  margin-left: 1rem;
}

.mr-1 {
  margin-right: 0.25rem;
}

.mr-2 {
  margin-right: 0.5rem;
}
</style>

