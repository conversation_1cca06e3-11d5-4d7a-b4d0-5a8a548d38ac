<template>
  <div v-if="visible" class="fixed inset-0 z-50 flex items-center justify-center">
    <!-- 遮罩层 -->
    <div class="absolute inset-0 bg-black bg-opacity-50 backdrop-blur-sm" @click="closeOnMaskClick && close()"></div>
    
    <!-- 弹窗卡片 -->
    <div class="bg-gray-800 rounded-lg shadow-2xl w-80 sm:w-96 relative overflow-hidden transform transition-all duration-300 ease-in-out">
      
      <!-- 顶部状态条 -->
      <div class="h-1 w-full" :class="typeBarClass"></div>
      
      <!-- 关闭按钮 -->
      <button @click="close" class="absolute top-3 right-3 text-gray-400 hover:text-white transition-colors duration-200">
        <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
          <path fill-rule="evenodd" d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z" clip-rule="evenodd" />
        </svg>
      </button>
      
      <!-- 内容区域 -->
      <div class="px-6 pt-6 pb-4">
        <div class="flex items-center mb-4">
          <!-- 图标 -->
          <div class="mr-3">
            <!-- 类型图标 -->
            <svg v-if="type === 'error'" xmlns="http://www.w3.org/2000/svg" class="h-6 w-6 text-red-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
            </svg>
            
            <svg v-else-if="type === 'warning'" xmlns="http://www.w3.org/2000/svg" class="h-6 w-6 text-yellow-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z" />
            </svg>
            
            <svg v-else xmlns="http://www.w3.org/2000/svg" class="h-6 w-6 text-blue-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
            </svg>
          </div>
          
          <!-- 标题 -->
          <div class="text-lg font-medium text-white">{{ title }}</div>
        </div>
        
        <!-- 消息内容 -->
        <div class="text-gray-300 leading-relaxed">{{ message }}</div>
      </div>
      
      <!-- 按钮区域 -->
      <div class="px-6 py-4 bg-gray-700 bg-opacity-30 flex justify-end">
        <button 
          @click="close" 
          class="px-4 py-2 rounded text-sm font-medium focus:outline-none transition-colors duration-200"
          :class="btnClass">
          {{ confirmText }}
        </button>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'SystemModal',
  model: {
    prop: 'visible',
    event: 'input'
  },
  props: {
    // 控制弹窗显示
    visible: {
      type: Boolean,
      default: false
    },
    // 弹窗标题
    title: {
      type: String,
      default: '系统提示'
    },
    // 弹窗内容
    message: {
      type: String,
      default: ''
    },
    // 弹窗类型：info, warning, error
    type: {
      type: String,
      default: 'info',
      validator: (value) => ['info', 'warning', 'error'].includes(value)
    },
    // 确认按钮文本
    confirmText: {
      type: String,
      default: '确定'
    },
    // 点击遮罩是否关闭
    closeOnMaskClick: {
      type: Boolean,
      default: true
    },
    // 自动关闭时间(ms)，0表示不自动关闭
    duration: {
      type: Number,
      default: 0
    }
  },
  data() {
    return {
      timer: null
    };
  },
  computed: {
    // 顶部状态条颜色
    typeBarClass() {
      switch(this.type) {
        case 'error': return 'bg-red-400';
        case 'warning': return 'bg-yellow-400';
        default: return 'bg-blue-400';
      }
    },
    // 按钮样式
    btnClass() {
      switch(this.type) {
        case 'error': return 'bg-red-500 hover:bg-red-600 text-white';
        case 'warning': return 'bg-yellow-500 hover:bg-yellow-600 text-white';
        default: return 'bg-blue-500 hover:bg-blue-600 text-white';
      }
    }
  },
  watch: {
    visible(newVal) {
      if (newVal && this.duration > 0) {
        this.startTimer();
      } else {
        this.clearTimer();
      }
    }
  },
  methods: {
    // 关闭弹窗
    close() {
      this.clearTimer();
      this.$emit('input', false);
      this.$emit('close');
    },
    // 开始计时器
    startTimer() {
      this.clearTimer();
      if (this.duration > 0) {
        this.timer = setTimeout(() => {
          this.close();
        }, this.duration);
      }
    },
    // 清除计时器
    clearTimer() {
      if (this.timer) {
        clearTimeout(this.timer);
        this.timer = null;
      }
    }
  },
  beforeDestroy() {
    this.clearTimer();
  }
};
</script>

<style scoped>
@keyframes modalFadeIn {
  from { opacity: 0; transform: scale(0.95); }
  to { opacity: 1; transform: scale(1); }
}

.fixed {
  animation: modalFadeIn 0.2s ease-out forwards;
}
</style> 