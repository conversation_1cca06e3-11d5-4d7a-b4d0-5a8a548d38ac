# 智能证件柜管理系统 - 技术栈文档

## 📋 项目概述

智能证件柜管理系统是一个基于 Electron + Vue2 开发的桌面应用程序，集成了人脸识别、指纹识别、静脉识别等生物识别技术，为证件管理提供安全可靠的解决方案。

**项目名称**: 证件柜 (icbBox)  
**版本**: 0.1.117  
**开发语言**: JavaScript, Vue.js  
**目标平台**: Windows, Linux  

## 🛠️ 核心技术栈

### 前端技术栈

#### 🎯 核心框架
- **Vue.js 2.6.14** - 渐进式 JavaScript 框架
  - 组件化开发架构
  - 响应式数据绑定
  - 虚拟 DOM 技术
  - 自定义指令系统

#### 🎨 UI 框架与样式
- **Element UI 2.15.14** - 基于 Vue 2.0 的桌面端组件库
  - 丰富的组件生态 (表单、表格、对话框等)
  - 完整的设计语言和主题系统
  - 国际化支持
- **TailwindCSS 3.3.5** - 实用优先的 CSS 框架
  - 原子化 CSS 类设计
  - 响应式设计支持
  - 自定义主题配置
- **Font Awesome 6.7.2** - 图标字体库
  - 丰富的图标资源
  - 矢量图标支持

#### 🚦 路由与状态管理
- **Vue Router 3.6.5** - Vue.js 官方路由管理器
  - 嵌套路由映射
  - 模块化、基于组件的路由配置
  - 路由守卫和权限控制
- **自定义状态管理** - 基于 Vuex 模式的状态管理
  - 集中式存储管理
  - 可预测的状态变更

### 桌面应用技术

#### 🖥️ 跨平台桌面应用
- **Electron 22.3.27** - 跨平台桌面应用开发框架
  - 主进程与渲染进程架构
  - 原生 API 访问能力
  - 跨平台兼容性 (Windows/Linux)
  - 单例模式实现
- **Electron Builder 23.6.0** - 应用打包和构建工具
  - 自动更新支持
  - 多平台构建 (Windows NSIS, Linux DEB)
  - 代码签名支持

#### 💾 数据存储
- **Electron Store 8.1.0** - Electron 应用数据持久化
  - 加密存储支持
  - JSON Schema 验证
  - 配置迁移支持

### 网络通信技术

#### 🌐 HTTP 客户端
- **Axios 1.7.9** - 基于 Promise 的 HTTP 库
  - 请求和响应拦截器
  - 自动 JSON 数据转换
  - 错误处理机制

#### 🔌 实时通信
- **WebSocket** - 双向实时通信
  - 与本地服务通信
  - 实时状态更新
  - 事件驱动架构

### 硬件集成技术

#### 🔍 生物识别技术栈
- **人脸识别引擎** - 基于 SeetaFace 6.0 系列
  - `SeetaFaceDetector600.dll` - 人脸检测
  - `SeetaFaceRecognizer610.dll` - 人脸识别
  - `SeetaFaceLandmarker600.dll` - 人脸关键点检测
  - `SeetaFaceAntiSpoofingX600.dll` - 活体检测
  - `SeetaQualityAssessor300.dll` - 人脸质量评估
  - `SeetaAgePredictor600.dll` - 年龄预测
  - `SeetaGenderPredictor600.dll` - 性别识别
  - `SeetaMaskDetector200.dll` - 口罩检测

- **指纹识别技术**
  - 多厂商指纹设备支持
  - 指纹模板存储与比对
  - 指纹质量评估

- **静脉识别技术**
  - 手指静脉图像采集
  - 静脉特征提取与比对
  - 活体检测

#### 📶 网络管理
- **Node-WiFi 2.0.16** - WiFi 网络管理
  - 无线网络扫描
  - 网络连接管理
  - 连接状态监控

### 开发工具链

#### 🔧 构建工具
- **Vue CLI 5.0.0** - Vue.js 标准开发工具
  - 项目脚手架生成
  - 插件系统
  - 开发服务器
- **Webpack** - 模块打包器
  - 代码分割
  - 热模块替换
  - 资源优化

#### 📝 代码质量工具
- **ESLint** - JavaScript 代码检查
  - Vue.js 专用规则
  - 代码风格统一
  - 自动错误修复
- **Babel** - JavaScript 编译器
  - ES6+ 语法转换
  - 浏览器兼容性处理

#### 🎨 样式处理工具
- **PostCSS 8.4.31** - CSS 后处理器
  - 自动添加浏览器前缀
  - CSS 优化和压缩
- **Autoprefixer 10.4.16** - CSS 前缀自动添加

### 图像处理技术

#### 🖼️ 图像处理库
- **Sharp 0.32.6** - 高性能图像处理
  - 图像缩放和裁剪
  - 格式转换
  - 图像优化
- **QRCode 1.5.4** - 二维码生成
  - 二维码生成和解析
  - 自定义样式支持

### 系统集成技术

#### 🗄️ 数据库技术
- **SQLite** - 轻量级关系数据库
  - 本地数据存储
  - 事务支持
  - 全文搜索功能

#### 🔐 安全技术
- **数据加密** - 敏感信息保护
  - AES 加密算法
  - 密钥安全管理
- **访问控制** - 权限管理系统
  - 基于角色的访问控制
  - 操作日志记录

## 🏗️ 系统架构

### 整体架构图
```
┌─────────────────────────────────────────────────────────────┐
│                    Electron 应用架构                        │
├─────────────────────────────────────────────────────────────┤
│  主进程 (Main Process) - background.js                      │
│  ├── 应用生命周期管理                                        │
│  ├── 窗口管理 (BrowserWindow)                               │
│  ├── 系统集成 (文件系统、硬件访问)                           │
│  ├── 进程管理 (wsServer 服务)                               │
│  └── IPC 通信 (与渲染进程通信)                               │
├─────────────────────────────────────────────────────────────┤
│  渲染进程 (Renderer Process)                                │
│  ├── Vue.js 2.6.14 应用框架                                 │
│  ├── Element UI 2.15.14 组件库                              │
│  ├── TailwindCSS 3.3.5 样式框架                             │
│  ├── Vue Router 3.6.5 路由管理                              │
│  └── preload.js 安全 API 桥接                               │
├─────────────────────────────────────────────────────────────┤
│  本地服务 (Native Service)                                  │
│  ├── document_cabinet.exe WebSocket 服务                    │
│  ├── SeetaFace 人脸识别引擎                                 │
│  ├── 指纹识别驱动                                           │
│  ├── 静脉识别驱动                                           │
│  └── SQLite 数据库                                          │
└─────────────────────────────────────────────────────────────┘
```

### 目录结构
```
src/
├── api/                    # API 接口层
├── assets/                 # 静态资源
├── components/             # 公共组件
├── config/                 # 配置文件
├── layouts/                # 布局组件
├── router/                 # 路由配置
├── utils/                  # 工具函数
├── views/                  # 页面组件
├── wsServer/               # 本地服务程序
├── App.vue                 # 根组件
├── background.js           # Electron 主进程
├── main.js                 # Vue 应用入口
└── preload.js             # 预加载脚本
```

## 🔧 开发环境

### 系统要求
- **Node.js**: >= 16.0.0
- **npm**: >= 8.0.0
- **操作系统**: Windows 10+, Ubuntu 18.04+

### 开发工具
- **IDE**: Visual Studio Code (推荐)
- **调试**: Vue DevTools, Electron DevTools
- **版本控制**: Git

## 🚀 性能指标

### 应用性能
- **启动时间**: < 3 秒
- **内存占用**: < 200MB
- **UI 响应**: < 100ms

### 识别性能
- **人脸识别**: < 500ms
- **指纹识别**: < 300ms
- **静脉识别**: < 800ms

## 🔒 安全特性

- **数据加密存储**
- **传输层安全 (TLS)**
- **生物特征模板化**
- **访问权限控制**
- **操作审计日志**

## 📦 部署方案

### Windows 平台
- **NSIS 安装包** - 完整安装程序
- **便携版** - 免安装运行

### Linux 平台
- **DEB 包** - Debian/Ubuntu 系统
- **AppImage** - 通用 Linux 格式

## 🔄 版本管理

- **语义化版本控制**
- **自动版本递增**
- **构建产物管理**
- **更新机制支持**

---

*本文档基于项目代码分析生成，反映了当前技术栈的真实状态。*
