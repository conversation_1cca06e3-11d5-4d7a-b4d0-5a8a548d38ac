<!-- 代码已包�?CSS：使�?TailwindCSS , 安装 TailwindCSS 后方可看到布局样式效果 -->

<template>
  <div class="min-h-screen tech-grid bg-gray-900">


    <div class="absolute top-8 right-8 z-50 flex gap-4">

      <SoftKeyboard :autoInitialize="false"></SoftKeyboard>
      <!-- 使用HomeButton组件替代原来的返回主页按钮 -->
      <HomeButton />

      <!-- 使用LogoutButton组件替代原来的返回登录按钮 -->
      <LogoutButton  />
    </div>


    <div
      class="container mx-auto px-4 h-screen flex flex-col items-center justify-center"
    >
      <!-- 标题 -->
      <h1 class="text-2xl font-bold mb-8 text-white relative">
        <span class="relative z-10">离线模式</span>
        <div class="absolute inset-0 blur-lg bg-primary opacity-20"></div>
      </h1>

      <!-- 提示信息 -->
      <div class="text-center mb-8 text-gray-300">
        <p v-if="!isinputing">
          {{ msg }} 点击
          <span
            @click="inputCode"
            class="text-blue-500 hover:text-blue-400 cursor-pointer"
            >【输入授权码】</span
          >。
        </p>
        <p v-else>
          若想重新拍照，请点击
          <span
            @click="isinputing = false"
            class="text-blue-500 hover:text-blue-400 cursor-pointer"
            >【返回】</span
          >。
        </p>
      </div>

      <!-- 二维码区域 -->
      <div class="tech-card mb-8 p-6" v-if="!isinputing">
        <!-- 这里放置实际的二维码图片 -->
        <div
          class="w-48 h-48 bg-gray-800 flex items-center justify-center border border-primary/30"
        >
          <img
            :src="qrCodeUrl"
            alt="授权二维码"
            v-if="qrCodeUrl"
            class="w-full h-full object-contain"
          />
          <div v-else class="text-gray-500">二维码加载中...</div>
        </div>
      </div>

      <!-- 授权码输入区域 -->
      <div class="w-full max-w-md" v-if="isinputing">
        <div class="tech-card p-6">
          <h2 class="text-lg font-semibold mb-4 text-center text-white">
            请输入授权码
          </h2>
          <div class="flex justify-center gap-2 mb-4">
            <!-- 5个授权码输入框 -->
            <input
              v-for="(_, index) in 5"
              :key="index"
              type="text"
              inputmode="numeric"
              pattern="\d*"
              maxlength="1"
              class="w-12 h-12 bg-gray-800 border-2 border-primary/30 rounded text-center text-xl text-white"
              @input="handleInput($event, index)"
              @keydown.delete="handleDelete($event, index)"
              ref="inputs"
            />
          </div>
          <!-- 错误提示 -->
          <p v-if="showError" class="text-red-500 text-center">
            {{ showErrorMsg }}
          </p>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import SoftKeyboard from "@/components/SoftKeyboard.vue";
import QRCode from "qrcode";
import storage from "@/utils/storage";
import LogoutButton from "@/components/LogoutButton.vue";
import HomeButton from "@/components/HomeButton.vue";
export default {
  name: "AuthorizeCode",
  components:{
    LogoutButton,
    HomeButton,
    SoftKeyboard
  },
  inject: ["speak", "websocketService", "websocketMsg", "speakSync"],
  watch: {
    websocketMsg: {
      handler(newMessage) {
        console.log("收到新的WebSocket消息取证消息:", newMessage);
        if (newMessage) {
          this.handleAuthorizeCodeMessages(newMessage);
        }
      },
      deep: true,
      immediate: true,
    },
  },
  data() {
    return {
      authCode: ["", "", "", "", ""],
      showError: false,
      showErrorMsg: "",
      isinputing: false,
      qrCodeUrl: "", // 存储生成的二维码URL
    };
  },
  created() {
    // 获取路由参数
    this.serverAuthCode = this.$route.params.authCode || "test98765";
    this.msg =
      this.$route.params.msg ||
      "当前非值班时间，若想取证，请将下方二维码拍给管理员后，";
  },
  mounted() {
    // 初始化时生成二维码
    this.$nextTick(() => {
      this.generateQRCode(this.serverAuthCode);
    });
  },
  methods: {

    async handleAuthorizeCodeMessages(message) {
      if (!message) return;
      // 根据消息类型处理不同的情况
      if (message && message.messages) {
        const userInfo = await storage.local.get("userInfo");
        switch (message.messages.type) {
          case 20011: //获取服务端配置项响应
            if (message.messages.data.code == 1) {
              this.showError = false;
              this.showErrorMsg = "";
              this.speak("授权码验证成功");
              this.$router.push({
                name: "pick",
                params: {  ...userInfo,authCode: this.serverAuthCode,
                  code: this.authCode.join(""),} },
              );
            } else {
              this.showError = true;
              this.showErrorMsg = message.messages.data.msg;
            }
            break;

          default:
          // console.log("未处理的消息类型:", message);
        }
      }
    },
    // 生成二维码的方法
    async generateQRCode(text) {
      try {
        // 生成二维码并转换为DataURL
        const url = await QRCode.toDataURL(text, {
          width: 192, // 对应w-48 (192px)
          margin: 1,
          color: {
            dark: "#FFFFFF", // 二维码颜色
            light: "#1F2937", // 背景色，对应bg-gray-800
          },
        });
        this.qrCodeUrl = url;
      } catch (err) {
        console.error("二维码生成失败:", err);
      }
    },
    inputCode() {
      this.isinputing = true;
      this.speak("请输入授权码");
      // 调用系统虚拟键盘
      // if (window.electronAPI && window.electronAPI.openOsk) {
      //   window.electronAPI.openOsk();
      // }
    },
    handleInput(event, index) {
      const value = event.target.value;
      // 只允许输入数字
      if (!/^\d*$/.test(value)) {
        event.target.value = "";
        return;
      }

      this.authCode[index] = value;

      // 自动跳转到下一个输入框
      if (value && index < 4) {
        this.$refs.inputs[index + 1].focus();
      }

      // 当所有格子都填满时，发送验证请求
      if (this.authCode.every((v) => v !== "")) {
        this.verifyCode();
      }
    },

    handleDelete(event, index) {
      if (event.key === "Backspace" && !this.authCode[index] && index > 0) {
        this.$refs.inputs[index - 1].focus();
      }
    },

    verifyCode() {
      let order = {
        type: 10011,
        id: "verifyCode",
        data: {
          authCode: this.serverAuthCode,
          code: this.authCode.join(""),
        },
      };
      this.websocketService.send(order);
    },

    sendMessage(data) {
      return this.websocketService.send(data);
    },
  },
};
</script>

<style scoped>
@keyframes glow {
  0% {
    box-shadow: 0 0 5px #0066ff;
  }
  50% {
    box-shadow: 0 0 20px #0066ff;
  }
  100% {
    box-shadow: 0 0 5px #0066ff;
  }
}

@keyframes borderFlow {
  0% {
    border-image-source: linear-gradient(0deg, #0066ff, #00a3ff);
  }
  50% {
    border-image-source: linear-gradient(180deg, #0066ff, #00a3ff);
  }
  100% {
    border-image-source: linear-gradient(360deg, #0066ff, #00a3ff);
  }
}

.tech-card {
  background: linear-gradient(
    135deg,
    rgba(0, 102, 255, 0.1),
    rgba(0, 163, 255, 0.1)
  );
  border: 2px solid;
  border-image-slice: 1;
  border-image-source: linear-gradient(45deg, #0066ff, #00a3ff);
  transition: all 0.3s ease;
  border-radius: 0.5rem;
}

.tech-card:hover {
  animation: glow 2s infinite;
  transform: scale(1.02);
}

.tech-grid {
  background-image: linear-gradient(rgba(0, 102, 255, 0.1) 1px, transparent 1px),
    linear-gradient(90deg, rgba(0, 102, 255, 0.1) 1px, transparent 1px);
  background-size: 20px 20px;
}

input {
  transition: all 0.3s ease;
}

input:focus {
  border-color: #0066ff;
  animation: glow 2s infinite;
  outline: none;
}

/* 禁用输入框的上下箭头 */
input::-webkit-outer-spin-button,
input::-webkit-inner-spin-button {
  -webkit-appearance: none;
  margin: 0;
}
</style>

