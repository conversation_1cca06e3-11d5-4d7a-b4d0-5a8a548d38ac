/**
 * 清理wsServer资源脚本
 * 用于在构建前确保所有wsServer进程已停止并且PID文件已删除
 */
const fs = require('fs');
const path = require('path');
const { execSync } = require('child_process');

console.log('正在清理wsServer资源...');

try {
  // 尝试终止所有wsServer进程
  if (process.platform === 'win32') {
    try {
      execSync('taskkill /F /IM document_cabinet.exe /T', { stdio: 'pipe' });
      console.log('已终止所有wsServer进程');
    } catch (error) {
      // 忽略进程不存在的错误
      console.log('没有找到需要终止的wsServer进程');
    }
  } else {
    try {
      execSync('pkill -f document_cabinet', { stdio: 'pipe' });
      console.log('已终止所有wsServer进程');
    } catch (error) {
      console.log('没有找到需要终止的wsServer进程');
    }
  }

  // 清理PID文件
  const rootDir = path.resolve(__dirname, '..');
  const possiblePidPaths = [
    path.join(rootDir, 'src', 'wsServer', 'document_cabinet.exe.pid'),
    // 可能的其他位置...
  ];

  // 尝试删除所有可能的PID文件
  let deletedFiles = 0;
  possiblePidPaths.forEach(pidPath => {
    try {
      if (fs.existsSync(pidPath)) {
        fs.unlinkSync(pidPath);
        console.log(`已删除PID文件: ${pidPath}`);
        deletedFiles++;
      }
    } catch (e) {
      console.error(`删除PID文件失败: ${pidPath}`, e);
    }
  });

  if (deletedFiles === 0) {
    console.log('没有找到需要删除的PID文件');
  }

  // 等待一小段时间确保系统释放所有资源
  console.log('等待系统释放资源...');
  const startTime = Date.now();
  // 等待2秒钟
  while (Date.now() - startTime < 2000) {
    // 空循环等待
  }

  console.log('wsServer资源清理完成');
} catch (error) {
  console.error('清理wsServer资源时出错:', error);
  // 不要因为清理失败而中断构建流程
} 