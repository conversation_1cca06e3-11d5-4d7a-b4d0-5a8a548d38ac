"use strict";

import { app, protocol, BrowserWindow, ipcMain } from "electron";
import { createProtocol } from "vue-cli-plugin-electron-builder/lib";
// import installExtension, { VUEJS_DEVTOOLS } from 'electron-devtools-installer'
const isDevelopment = process.env.NODE_ENV !== "production";
const Store = require("electron-store");
const path = require("path");
const fs = require("fs");
const { spawn, exec } = require("child_process");
const FileUtils = require("./utils/fileUtils").default;
const WifiUtils = require("./utils/wifiUtils");
// const FingerVeinUtils = require("./utils/fingerVeinUtils").default;

// 实现应用单例模式
const gotTheLock = app.requestSingleInstanceLock();

if (!gotTheLock) {
  console.log('已有一个实例正在运行，退出当前实例');
  app.quit();
} else {
  app.on('second-instance', (event, commandLine, workingDirectory) => {
    // 当运行第二个实例时，聚焦到第一个实例的窗口
    if (BrowserWindow.getAllWindows().length) {
      const mainWindow = BrowserWindow.getAllWindows()[0];
      if (mainWindow.isMinimized()) mainWindow.restore();
      mainWindow.focus();
    }
  });

  // 初始化 electron-store
  const store = new Store();

  // wsServer进程管理
  let wsServerProcess = null;
  let wsServerPath = null;

  // 启动wsServer服务
  function startWsServer() {
    try {
      // 如果进程已经存在，则不再重复启动
      if (wsServerProcess !== null) {
        console.log("wsServer进程已存在，不再重复启动");
        return true;
      }

      // 检查系统中是否已经有服务在运行
      return isWsServerRunning().then(isRunning => {
        if (isRunning) {
          console.log("系统中已存在运行的wsServer进程，不再重复启动");
          return true;
        }

        // 确定wsServer路径
        if (isDevelopment) {
          // 开发环境：使用项目根目录下的src/wsServer目录
          const rootDir = path.resolve(__dirname, '..');
          wsServerPath = path.join(rootDir, "src", "wsServer", "document_cabinet.exe");

          console.log("wsServerPath开发环境", wsServerPath);
        } else {
          // 生产环境：使用resources目录下的wsServer
          // process.resourcesPath 指向 安装目录\resources
          wsServerPath = path.join(process.resourcesPath, "wsServer", "document_cabinet.exe");
          
          console.log("wsServerPath生产环境", wsServerPath);
          console.log("process.resourcesPath:", process.resourcesPath);
        }

        console.log("wsServer路径:", wsServerPath);

        // 检查文件是否存在
        if (!fs.existsSync(wsServerPath)) {
          console.error("wsServer可执行文件不存在:", wsServerPath);
          return false;
        }

        // 获取wsServer工作目录
        const wsServerDir = path.dirname(wsServerPath);
        console.log("wsServer工作目录:", wsServerDir);

        // 启动wsServer进程
        wsServerProcess = spawn(wsServerPath, [], {
          cwd: wsServerDir,
          stdio: ["pipe", "pipe", "pipe"],
          detached: false,
        });

        console.log("wsServer进程已启动，PID:", wsServerProcess.pid);

        // 监听stdout
        wsServerProcess.stdout.on("data", (data) => {
          console.log("wsServer输出:", data.toString());
        });

        // 监听stderr
        wsServerProcess.stderr.on("data", (data) => {
          console.error("wsServer错误:", data.toString());
        });

        // 监听进程退出
        wsServerProcess.on("close", (code) => {
          console.log("wsServer进程已退出，退出码:", code);
          wsServerProcess = null;
        });

        // 监听进程错误
        wsServerProcess.on("error", (error) => {
          console.error("wsServer进程错误:", error);
          wsServerProcess = null;
        });

        return true;
      });
    } catch (error) {
      console.error("启动wsServer失败:", error);
      return false;
    }
  }

  // 停止wsServer服务
  function stopWsServer() {
    if (wsServerProcess) {
      try {
        console.log("正在停止wsServer进程...");
        // 先尝试正常终止进程
        wsServerProcess.kill("SIGTERM");
        
        // 在Windows上使用taskkill强制结束进程，确保彻底终止
        const pidFilePath = path.join(path.dirname(wsServerPath), "document_cabinet.exe.pid");
        
        // 等待进程优雅退出
        setTimeout(() => {
          if (wsServerProcess && !wsServerProcess.killed) {
            console.log("强制终止wsServer进程...");
            
            // 使用taskkill强制终止进程
            try {
              if (process.platform === "win32") {
                exec(`taskkill /F /PID ${wsServerProcess.pid}`, (error) => {
                  if (error) {
                    console.error("taskkill终止进程失败:", error);
                  } else {
                    console.log("成功使用taskkill终止进程");
                  }
                  
                  // 尝试删除PID文件
                  try {
                    if (fs.existsSync(pidFilePath)) {
                      fs.unlinkSync(pidFilePath);
                      console.log("成功删除PID文件:", pidFilePath);
                    }
                  } catch (fsError) {
                    console.error("删除PID文件失败:", fsError);
                  }
                });
              } else {
                wsServerProcess.kill("SIGKILL");
              }
            } catch (killError) {
              console.error("强制终止进程失败:", killError);
            }
          } else {
            // 进程已经退出，尝试删除PID文件
            try {
              if (fs.existsSync(pidFilePath)) {
                fs.unlinkSync(pidFilePath);
                console.log("成功删除PID文件:", pidFilePath);
              }
            } catch (fsError) {
              console.error("删除PID文件失败:", fsError);
            }
          }
        }, 2000);

        wsServerProcess = null;
      } catch (error) {
        console.error("停止wsServer失败:", error);
      }
    } else {
      // 如果进程对象不存在，但可能有残留的进程和文件
      // 尝试通过进程名终止
      if (process.platform === "win32") {
        exec('taskkill /F /IM document_cabinet.exe', (error) => {
          if (error) {
            console.error("通过进程名终止wsServer失败:", error);
          } else {
            console.log("通过进程名成功终止wsServer");
          }
        });
        
        // 尝试删除PID文件
        const wsServerDir = isDevelopment 
          ? path.join(path.resolve(__dirname, '..'), "src", "wsServer")
          : path.join(process.resourcesPath, "wsServer");
          
        const pidFilePath = path.join(wsServerDir, "document_cabinet.exe.pid");
        
        try {
          if (fs.existsSync(pidFilePath)) {
            fs.unlinkSync(pidFilePath);
            console.log("成功删除PID文件:", pidFilePath);
          }
        } catch (fsError) {
          console.error("删除PID文件失败:", fsError);
        }
      }
    }
  }

  // 检查wsServer是否运行
  function isWsServerRunning() {
    return new Promise((resolve) => {
      exec(
        'tasklist /FI "IMAGENAME eq document_cabinet.exe" /FO CSV',
        (error, stdout) => {
          if (error) {
            resolve(false);
            return;
          }
          // 检查输出中是否包含document_cabinet.exe
          resolve(stdout.includes("document_cabinet.exe"));
        }
      );
    });
  }

  // Scheme must be registered before the app is ready
  protocol.registerSchemesAsPrivileged([
    { scheme: "app", privileges: { secure: true, standard: true } },
  ]);

  // 注册IPC通信处理器
  function registerIpcHandlers() {
    // 读取文件
    ipcMain.handle("file:read", async (event, filePath) => {
      try {
        return await FileUtils.readFile(filePath);
      } catch (error) {
        throw new Error(`读取文件失败: ${error.message}`);
      }
    });

    // 写入文件
    ipcMain.handle("file:write", async (event, { filePath, content }) => {
      try {
        await FileUtils.writeFile(filePath, content);
        return true;
      } catch (error) {
        throw new Error(`写入文件失败: ${error.message}`);
      }
    });

    // 选择文件
    ipcMain.handle("file:select", async (event, options) => {
      try {
        return await FileUtils.selectFile(options);
      } catch (error) {
        throw new Error(`选择文件失败: ${error.message}`);
      }
    });

    // 选择用户导入文件
    ipcMain.handle("select-file", async (event, options) => {
      try {
        console.log("收到select-file请求，参数:", options);
        return await FileUtils.selectImportFile(options);
      } catch (error) {
        console.error("选择导入文件失败:", error);
        throw new Error(`选择导入文件失败: ${error.message}`);
      }
    });

    // 选择保存路径
    ipcMain.handle("file:save-dialog", async (event, options) => {
      try {
        return await FileUtils.selectSavePath(options);
      } catch (error) {
        throw new Error(`选择保存路径失败: ${error.message}`);
      }
    });

    // 注册WiFi相关IPC处理程序
    WifiUtils.registerIpcHandlers();

    // 注册人脸识别相关IPC处理程序

    // 注册指静脉识别相关IPC处理程序
    registerFingerVeinHandlers();

    // 打开Windows虚拟键盘

    // Windows系统调用屏幕键盘
    function showWindowsOSK() {
      const oskPath = path.join("C:\\Windows", "System32", "osk.exe");
      console.log(`尝试显示键盘 ${oskPath}`);

      try {
        exec('start "" "C:\\Windows\\System32\\osk.exe"', (error) => {
          if (error) {
            console.log(`打开屏幕键盘出错: ${error}`);
          } else {
            console.log("显示键盘成功");
          }
        });
      } catch (error) {
        console.log(`启动键盘进程出错: ${error}`);
      }
    }
    ipcMain.handle("open-osk", async () => {
      console.log("open-oskopen-oskopen-oskopen-osk")
      // const { exec } = require('child_process');
      // exec('osk.exe');
      if (process.platform === "win32") {
        showWindowsOSK();
      } else if (process.platform === "linux") {
        exec("onboard"); // Linux系统
      }
      return { success: true };
    });

    // 关闭Windows虚拟键盘

    // Windows系统关闭屏幕键盘
    function closeWindowsOSK() {
      console.log("尝试关闭键盘");
      // 使用powershell以管理员权限执行taskkill命令
      const command =
        "powershell.exe -Command \"Start-Process -FilePath 'taskkill' -ArgumentList '/F', '/IM', 'osk.exe', '/T' -Verb RunAs -WindowStyle Hidden\"";

      exec(command, (error, stdout, stderr) => {
        if (error) {
          console.log(`关闭屏幕键盘出错: ${error}`);
          // 如果第一次尝试失败，使用备用方法，测试时候没有问题，打包线上运行的时候会出现关闭失败问题，这个备用方法就可以解决关闭失败问题。
          exec("taskkill /IM osk.exe", (err) => {
            if (err) {
              console.log(`备用方法关闭屏幕键盘也失败: ${err}`);
            } else {
              console.log("使用备用方法成功关闭键盘");
            }
          });
        } else {
          console.log("关闭键盘成功");
        }

        if (stderr) {
          console.log(`关闭键盘stderr输出: ${stderr}`);
        }
      });
    }

    ipcMain.handle("close-osk", async () => {
      console.log(`关闭键盘 ${process.platform}`);
      if (process.platform === "win32") {
        closeWindowsOSK();
      } else if (process.platform === "linux") {
        exec("killall onboard");
      }
      return { success: true };
    });

    // wsServer管理相关IPC处理程序
    ipcMain.handle("ws-server:start", async () => {
      try {
        const success = await startWsServer();
        return {
          success,
          message: success ? "wsServer启动成功" : "wsServer启动失败",
        };
      } catch (error) {
        return { success: false, message: `启动wsServer失败: ${error.message}` };
      }
    });

    ipcMain.handle("ws-server:stop", async () => {
      try {
        stopWsServer();
        return { success: true, message: "wsServer停止成功" };
      } catch (error) {
        return { success: false, message: `停止wsServer失败: ${error.message}` };
      }
    });

    ipcMain.handle("ws-server:status", async () => {
      try {
        const isRunning = await isWsServerRunning();
        return {
          success: true,
          isRunning,
          pid: wsServerProcess ? wsServerProcess.pid : null,
          message: isRunning ? "wsServer正在运行" : "wsServer未运行",
        };
      } catch (error) {
        return {
          success: false,
          message: `检查wsServer状态失败: ${error.message}`,
        };
      }
    });
  }

  // 注册人脸识别相关IPC处理程序

  // 注册指静脉识别相关IPC处理程序
  function registerFingerVeinHandlers() {
    // 空实现，避免调用失败
    console.log("指静脉功能暂不可用");
  }

  async function createWindow() {
    // Create the browser window.
    const preloadPath = process.env.WEBPACK_DEV_SERVER_URL
      ? path.join(__dirname, "../src/preload.js")
      : path.join(__dirname, "preload.js");

    // 创建托盘图标

    console.log("Using preload path:", preloadPath);
    console.log("Preload file exists:", fs.existsSync(preloadPath));
    var BrowserWindoWonfig = {}
    if (isDevelopment) {
      BrowserWindoWonfig = {
        width: 1920,
        height: 1080,
        frame: true, // 禁用标准窗口框架
        title: '证件柜',
        fullscreen: false,
        titleBarStyle: 'show',
        show: false, // 修改为false，等待ready-to-show事件后再显示
      
        webPreferences: {
          disableHardwareAcceleration:true,
          max0ldSpaceSize:1024,
          nodeIntegration: false,
          contextIsolation: true,
          preload: preloadPath,
          // 开启调试
          devTools: true,
         
          // 启用媒体设备访问
          webSecurity: true,
          allowRunningInsecureContent: false
        }
      }
    }else{

      // BrowserWindoWonfig = {
      //   width: 1920,
      //   height: 1080,
      //   frame: true, // 禁用标准窗口框架
      //   title: '证件柜',
      //   fullscreen: false,
      //   titleBarStyle: 'show',
      //   show: false, // 修改为false，等待ready-to-show事件后再显示
      
      //   webPreferences: {
      //     disableHardwareAcceleration:true,
      //     max0ldSpaceSize:1024,
      //     nodeIntegration: false,
      //     contextIsolation: true,
      //     preload: preloadPath,
      //     // 开启调试
      //     devTools: true,
         
      //     // 启用媒体设备访问
      //     webSecurity: true,
      //     allowRunningInsecureContent: false
      //   }
      // }

      BrowserWindoWonfig = {
          width: 1920,
      height: 1080,
      frame: false, // 禁用标准窗口框架
      title: "证件柜",
      fullscreen: true,
      resizable: false, // 禁止用户调整窗口大小
      titleBarStyle: "hidden",
      show: false, // 修改为false，等待ready-to-show事件后再显示
      titleBarOverlay: false, // 确保不显示标题栏
      transparent: true,
      thickFrame: false, // 禁用窗口边框
      autoHideMenuBar: true, // 自动隐藏菜单栏

      webPreferences: {
        nodeIntegration: false,
        contextIsolation: true,
        preload: preloadPath,
        // 开启调试
        devTools: true,

        // 启用媒体设备访问
        webSecurity: true,
        allowRunningInsecureContent: false,
      },
      }
    }
    const win = new BrowserWindow({
      ...BrowserWindoWonfig
    });

    // 监听ready-to-show事件，在页面加载完成后再显示窗口
    win.once("ready-to-show", () => {
      win.show();
      if (isDevelopment) {
        win.setFullScreen(false); // 确保窗口显示时处于全屏状态
      }else{
        win.setFullScreen(true); // 确保窗口显示时处于全屏状态
      }
    });

    // 监听窗口大小变化，确保始终保持全屏
    win.on("leave-full-screen", () => {
      win.setFullScreen(true);
    });

    // 设置权限处理器，允许访问媒体设备
    win.webContents.session.setPermissionRequestHandler(
      (webContents, permission, callback) => {
        // 允许摄像头和麦克风访问权限
        if (
          permission === "media" ||
          permission === "camera" ||
          permission === "microphone"
        ) {
          callback(true);
          return;
        }
        callback(false);
      }
    );

    if (process.env.WEBPACK_DEV_SERVER_URL) {
      // Load the url of the dev server if in development mode
      await win.loadURL(process.env.WEBPACK_DEV_SERVER_URL);
      if (!process.env.IS_TEST) win.webContents.openDevTools();
    } else {
      createProtocol("app");
      // Load the index.html when not in development
      win.loadURL("app://./index.html");
    }

    // 添加 IPC 处理器
    ipcMain.handle("electron-store-get", async (event, val) => {
      console.log("Getting value for key:", val);
      const result = store.get(val);
      console.log("Retrieved value:", result);
      return result;
    });

    ipcMain.handle("electron-store-set", async (event, key, val) => {
      console.log("Setting value:", key, val);
      store.set(key, val);
      return true;
    });

    // 注册所有IPC处理程序
    registerIpcHandlers();

    // 在 background.js 中添加调试代码
    const devPreloadPath = path.join(__dirname, "../src/preload.js");
    const prodPreloadPath = path.join(__dirname, "preload.js");
    console.log("开发环境 preload 路径:", devPreloadPath);
    console.log("生产环境 preload 路径:", prodPreloadPath);
    console.log("开发环境文件存在:", fs.existsSync(devPreloadPath));
    console.log("生产环境文件存在:", fs.existsSync(prodPreloadPath));

    // registerIpcHandlers()
  }

  // Quit when all windows are closed.
  app.on("window-all-closed", () => {
    // On macOS it is common for applications and their menu bar
    // to stay active until the user quits explicitly with Cmd + Q
    if (process.platform !== "darwin") {
      app.quit();
    }
  });

  app.on("activate", () => {
    // On macOS it's common to re-create a window in the app when the
    // dock icon is clicked and there are no other windows open.
    if (BrowserWindow.getAllWindows().length === 0) createWindow();
  });

  // 彻底清理wsServer资源，确保没有残留进程和文件
  function cleanupWsServerResources() {
    return new Promise((resolve) => {
      console.log("正在彻底清理wsServer资源...");
      
      // 首先停止已知的进程
      stopWsServer();
      
      // 强制终止所有可能的wsServer进程
      if (process.platform === "win32") {
        exec('taskkill /F /IM document_cabinet.exe /T', (error) => {
          if (error) {
            console.log("无wsServer进程需要清理，或清理失败:", error);
          } else {
            console.log("成功终止所有wsServer进程");
          }
          
          // 清理所有可能的PID文件
          try {
            // 确定可能的wsServer目录位置
            const devWsServerDir = path.join(path.resolve(__dirname, '..'), "src", "wsServer");
            const prodWsServerDir = process.resourcesPath ? path.join(process.resourcesPath, "wsServer") : null;
            
            const possiblePidPaths = [
              path.join(devWsServerDir, "document_cabinet.exe.pid"),
            ];
            
            if (prodWsServerDir) {
              possiblePidPaths.push(path.join(prodWsServerDir, "document_cabinet.exe.pid"));
            }
            
            // 尝试删除所有可能位置的PID文件
            possiblePidPaths.forEach(pidPath => {
              try {
                if (fs.existsSync(pidPath)) {
                  fs.unlinkSync(pidPath);
                  console.log("成功删除PID文件:", pidPath);
                }
              } catch (e) {
                console.error("删除PID文件失败:", pidPath, e);
              }
            });
          } catch (e) {
            console.error("清理PID文件过程出错:", e);
          }
          
          // 延迟一点时间确保系统有时间释放资源
          setTimeout(resolve, 1000);
        });
      } else {
        // 非Windows系统
        exec('pkill -f document_cabinet', () => {
          setTimeout(resolve, 1000);
        });
      }
    });
  }

  // 修改app.on("ready")处理程序
  app.on("ready", async () => {
    if (isDevelopment && !process.env.IS_TEST) {
      // Install Vue Devtools
      try {
        // await installExtension(VUEJS_DEVTOOLS)
        // console.log('Vue Devtools installed successfully')
      } catch (e) {
        console.warn("Vue Devtools installation failed:", e.toString());
        console.log("You can still use the application without Vue Devtools");
      }
    }

    // 先清理wsServer资源，再启动新的服务
    await cleanupWsServerResources();

    // 启动wsServer服务
    console.log("正在启动wsServer服务...");
    try {
      const wsServerStarted = await startWsServer();
      if (wsServerStarted) {
        console.log("wsServer服务启动成功");
      } else {
        console.error("wsServer服务启动失败");
      }
    } catch (error) {
      console.error("wsServer服务启动出错:", error);
    }

    createWindow();
  });

  // 修改before-quit处理程序
  app.on("before-quit", async () => {
    console.log("应用即将退出，正在停止wsServer服务...");
    // 使用增强的清理功能
    await cleanupWsServerResources();
  });

  // 修改window-all-closed处理程序
  app.on("window-all-closed", async () => {
    console.log("所有窗口已关闭，正在停止wsServer服务...");
    // 使用增强的清理功能
    await cleanupWsServerResources();

    // On macOS it is common for applications and their menu bar
    // to stay active until the user quits explicitly with Cmd + Q
    if (process.platform !== "darwin") {
      app.quit();
    }
  });

  // Exit cleanly on request from parent process in development mode.
  if (isDevelopment) {
    if (process.platform === "win32") {
      process.on("message", (data) => {
        if (data === "graceful-exit") {
          app.quit();
        }
      });
    } else {
      process.on("SIGTERM", () => {
        app.quit();
      });
    }
  }
}
