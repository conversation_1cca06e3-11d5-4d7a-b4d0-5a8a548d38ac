/**
 * Vue 自定义指令集合
 */

// 判断当前是否为移动设备
const isMobileDevice = () => {
  return /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent);
};

/**
 * 移动设备 Select 优化指令
 * 使用方法：v-mobile-select
 * 
 * 在移动设备上优化 el-select 组件，防止触屏键盘弹出导致的卡顿问题
 */
export const mobileSelect = {
  bind(el) {
    if (!isMobileDevice()) return;
    
    // 找到 input 元素
    const input = el.querySelector('.el-input__inner');
    if (!input) return;
    
    // 设置为只读，阻止键盘弹出
    input.setAttribute('readonly', 'readonly');
    
    // 点击时手动触发下拉框
    const handleClick = (e) => {
      // 阻止默认行为
      e.preventDefault();
      
      // 获取 Vue 组件实例并手动触发下拉框
      const select = el.__vue__;
      if (select) {
        select.visible = true;
      }
    };
    
    // 添加点击事件监听
    input.addEventListener('click', handleClick);
    
    // 保存事件引用以便解绑
    el._mobileSelectClickHandler = handleClick;
  },
  
  // 组件更新时重新绑定
  update(el) {
    if (!isMobileDevice()) return;
    
    const input = el.querySelector('.el-input__inner');
    if (!input) return;
    
    // 确保属性仍然存在
    input.setAttribute('readonly', 'readonly');
  },
  
  // 解绑事件监听
  unbind(el) {
    if (!isMobileDevice()) return;
    
    const input = el.querySelector('.el-input__inner');
    if (!input || !el._mobileSelectClickHandler) return;
    
    input.removeEventListener('click', el._mobileSelectClickHandler);
    delete el._mobileSelectClickHandler;
  }
};

// 所有指令的集合
export default {
  mobileSelect
}; 