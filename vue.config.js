const { defineConfig } = require('@vue/cli-service')
const path = require('path')
const CopyWebpackPlugin = require('copy-webpack-plugin')

module.exports = defineConfig({
  transpileDependencies: true,
  // 设置publicPath为相对路径，解决图标路径问题
  publicPath: './',
  // 添加Element UI字体文件处理配置
  css: {
    extract: false, // 不提取CSS到单独文件，解决字体图标路径问题
    loaderOptions: {
      css: {
        url: {
          filter: (url) => {
            // 对于字体文件，返回false表示不处理URL
            if (url.includes('element-icons')) {
              return false;
            }
            return true;
          }
        }
      }
    }
  },
  chainWebpack: config => {
    // 处理字体文件
    config.module
      .rule('fonts')
      .test(/\.(woff2?|eot|ttf|otf)(\?.*)?$/)
      .use('url-loader')
      .loader('url-loader')
      .options({
        limit: 1000000, // 将字体文件转为base64内联到CSS中
        name: 'fonts/[name].[hash:8].[ext]',
        publicPath: './'
      })
      .end();

    // 添加复制插件
    config
      .plugin('copy-element-fonts')
      .use(CopyWebpackPlugin, [
        {
          patterns: [
            {
              from: path.resolve(__dirname, 'node_modules/element-ui/lib/theme-chalk/fonts'),
              to: path.resolve(__dirname, 'dist_electron/bundled/fonts')
            }
          ]
        }
      ]);
  },
  pluginOptions: {
    electronBuilder: {
      preload: path.join(__dirname, 'src/preload.js'),
      nodeIntegration: false,
      contextIsolation: true,
      builderOptions: {
        appId: "com.example.icBox",
        productName: "证件柜",
        directories: {
          output: "dist_electron"
        },
        win: {
          icon: "build/icons/icon.ico",
          target: [
            {
              target: "nsis",
              arch: ["x64"]
            }
          ]
        },
        mac: {
          icon: "build/icons/icon.icns"
        },
        linux: {
          icon: "build/icons/png",  // 文件夹路径，会自动使用不同尺寸
          target: ["deb", "AppImage"],  // 支持deb格式和AppImage格式
          category: "Utility",
          maintainer: "维护者姓名",
          vendor: "供应商名称",
          synopsis: "证件管理系统",  // 简短描述
          desktop: {  // 桌面启动器配置
            Name: "证件柜",
            Comment: "证件管理系统",
            Categories: "Utility;"
          }
        },
        deb: {  // .deb特定配置
          depends: ["libnotify4", "libxtst6", "libnss3"],  // 常见依赖
          afterInstall: "build/linux/after-install.sh",  // 安装后脚本（可选）
          afterRemove: "build/linux/after-remove.sh"  // 卸载后脚本（可选）
        },
        nsis: {
          oneClick: false,
          allowToChangeInstallationDirectory: true,
          perMachine: false,
          deleteAppDataOnUninstall: true,
          createDesktopShortcut: true,
          createStartMenuShortcut: true,
          shortcutName: "证件柜"

         
        }
      }
    }
  }
})
