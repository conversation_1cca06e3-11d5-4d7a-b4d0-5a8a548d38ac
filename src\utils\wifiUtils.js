'use strict'

const wifi = require('node-wifi')
const { ipcMain } = require('electron')

// 初始化WiFi模块
wifi.init({
  iface: null // 使用默认网络接口
})

/**
 * 获取当前连接的WiFi信息
 * @returns {Promise<Object>} WiFi信息对象
 */
const getCurrentConnection = async () => {
  try {
    const connection = await wifi.getCurrentConnections()
    return connection && connection.length > 0 ? connection[0] : null
  } catch (error) {
    console.error('获取WiFi连接信息失败:', error)
    return null
  }
}

/**
 * 获取WiFi信号强度级别
 * @param {number} signal_level - 信号强度值(dBm)
 * @returns {number} 信号强度级别(0-4)
 */
const getSignalStrength = (signal_level) => {
  if (!signal_level) return 0
  
  // 信号强度通常在-30到-90之间
  // 转换为0-4的级别
  if (signal_level >= -50) return 4      // 极好
  if (signal_level >= -60) return 3      // 很好
  if (signal_level >= -70) return 2      // 好
  if (signal_level >= -80) return 1      // 一般
  return 0                               // 差
}

/**
 * 注册IPC处理程序
 */
const registerIpcHandlers = () => {
  // 获取当前WiFi连接信息
  ipcMain.handle('wifi:get-current', async () => {
    try {
      const connection = await getCurrentConnection()
      if (connection) {
        return {
          ssid: connection.ssid,
          signal_level: connection.signal_level,
          quality: connection.quality,
          security: connection.security,
          strength: getSignalStrength(connection.signal_level)
        }
      }
      return null
    } catch (error) {
      console.error('获取WiFi信息失败:', error)
      return null
    }
  })
}

module.exports = {
  getCurrentConnection,
  getSignalStrength,
  registerIpcHandlers
}