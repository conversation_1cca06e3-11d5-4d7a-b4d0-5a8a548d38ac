/**
 * wsServer服务管理工具类
 * 提供wsServer的启动、停止、状态检查等功能
 */

const { ipc<PERSON><PERSON><PERSON> } = require('electron')

class WsServerUtils {
  /**
   * 启动wsServer服务
   * @returns {Promise<Object>} 启动结果
   */
  static async startServer() {
    try {
      const result = await ipcRenderer.invoke('ws-server:start')
      return result
    } catch (error) {
      console.error('启动wsServer失败:', error)
      return {
        success: false,
        message: `启动wsServer失败: ${error.message}`
      }
    }
  }

  /**
   * 停止wsServer服务
   * @returns {Promise<Object>} 停止结果
   */
  static async stopServer() {
    try {
      const result = await ipcRenderer.invoke('ws-server:stop')
      return result
    } catch (error) {
      console.error('停止wsServer失败:', error)
      return {
        success: false,
        message: `停止wsServer失败: ${error.message}`
      }
    }
  }

  /**
   * 检查wsServer服务状态
   * @returns {Promise<Object>} 状态信息
   */
  static async getServerStatus() {
    try {
      const result = await ipcRenderer.invoke('ws-server:status')
      return result
    } catch (error) {
      console.error('检查wsServer状态失败:', error)
      return {
        success: false,
        isRunning: false,
        message: `检查wsServer状态失败: ${error.message}`
      }
    }
  }

  /**
   * 重启wsServer服务
   * @returns {Promise<Object>} 重启结果
   */
  static async restartServer() {
    try {
      // 先停止服务
      const stopResult = await this.stopServer()
      if (!stopResult.success) {
        return {
          success: false,
          message: `停止wsServer失败: ${stopResult.message}`
        }
      }

      // 等待一段时间确保进程完全停止
      await new Promise(resolve => setTimeout(resolve, 2000))

      // 重新启动服务
      const startResult = await this.startServer()
      return {
        success: startResult.success,
        message: startResult.success ? 'wsServer重启成功' : `wsServer重启失败: ${startResult.message}`
      }
    } catch (error) {
      console.error('重启wsServer失败:', error)
      return {
        success: false,
        message: `重启wsServer失败: ${error.message}`
      }
    }
  }

  /**
   * 检查wsServer是否健康运行
   * @returns {Promise<boolean>} 是否健康
   */
  static async isHealthy() {
    try {
      const status = await this.getServerStatus()
      return status.success && status.isRunning
    } catch (error) {
      console.error('检查wsServer健康状态失败:', error)
      return false
    }
  }
}

export default WsServerUtils 