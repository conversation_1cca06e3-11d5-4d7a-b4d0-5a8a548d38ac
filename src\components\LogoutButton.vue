<!-- 返回登录按钮组件 -->
<template>
  <div>
    <!-- 返回登录按钮 -->
    <button 
      class="text-white flex items-center space-x-2 !rounded-button whitespace-nowrap hover:opacity-80 cursor-pointer p-2"
      @click="showLogoutConfirm"
    >
      <i class="fas fa-sign-out-alt text-lg"></i>
      <span>{{ buttonText }}</span>
    </button>

    <!-- 使用退出登录确认对话框组件 -->
    <LogoutConfirmDialog 
      :visible="showConfirmDialog"
      :title="dialogTitle"
      :message="dialogMessage"
      :cancelText="cancelText"
      :confirmText="confirmText"
      @cancel="cancelLogout"
      @confirm="confirmLogout"
    />
  </div>
</template>

<script>
import LogoutConfirmDialog from '@/components/LogoutConfirmDialog.vue';
// import storage from '@/utils/storage';

export default {
  name: 'LogoutButton',
  components: {
    LogoutConfirmDialog
  },
  props: {
    // 按钮文本
    buttonText: {
      type: String,
      default: '返回登录'
    },
    // 对话框标题
    dialogTitle: {
      type: String,
      default: '确认退出登录'
    },
    // 对话框消息
    dialogMessage: {
      type: String,
      default: '您确定要退出当前登录吗？'
    },
    // 取消按钮文本
    cancelText: {
      type: String,
      default: '取消'
    },
    // 确认按钮文本
    confirmText: {
      type: String,
      default: '确认退出'
    }
  },
  data() {
    return {
      // 确认对话框显示状态
      showConfirmDialog: false,
      // 上次点击时间，用于防止快速连续点击
      lastClickTime: 0
    };
  },
  methods: {
    // 显示退出登录确认对话框
    showLogoutConfirm() {
      const now = new Date().getTime();
      // 防止快速连续点击（300ms内）
      if (now - this.lastClickTime < 300) {
        console.log("点击过于频繁，忽略此次点击");
        return;
      }
      this.lastClickTime = now;
      this.showConfirmDialog = true;
    },
    
    // 取消退出登录
    cancelLogout() {
      this.showConfirmDialog = false;
      console.log("用户取消退出登录");
      this.$emit('cancel');
    },
    
    // 确认退出登录
    confirmLogout() {
      console.log("用户确认退出登录");
      // 清除本地存储中的token和用户信息
      // storage.local.remove('token');
      // storage.local.remove('userInfo');
      // 隐藏对话框
      this.showConfirmDialog = false;
      // 发送确认事件
      this.$emit('confirm');
      // 跳转到首页
      this.$router.push("/");
    }
  }
};
</script> 