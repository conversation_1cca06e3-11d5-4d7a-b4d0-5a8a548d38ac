const { contextBridge, ipc<PERSON>enderer } = require('electron')

console.log('Preload script is running...')

// 暴露文件操作API到window对象
contextBridge.exposeInMainWorld('electronAPI', {
  // 文件操作
  readFile: (filePath) => {
    console.log('Calling readFile with:', filePath)
    return ipcRenderer.invoke('file:read', filePath)
  },
  writeFile: (filePath, content) => {
    console.log('Calling writeFile with:', filePath, content)
    return ipcRenderer.invoke('file:write', { filePath, content })
  },
  selectFile: (options) => {
    console.log('Calling selectFile with:', options)
    return ipcRenderer.invoke('file:select', options)
  },
  selectSavePath: (options) => {
    console.log('Calling selectSavePath with:', options)
    return ipcRenderer.invoke('file:save-dialog', options)
  },
  
  // 添加对select-file的支持
  selectImportFile: (options) => {
    console.log('Calling selectImportFile with:', options)
    return ipcRenderer.invoke('select-file', options)
  },
  
  // electron-store操作
  storeGet: (key) => ipcRenderer.invoke('electron-store-get', key),
  storeSet: (key, value) => ipcRenderer.invoke('electron-store-set', key, value),
  
  // WiFi相关操作
  getWifiInfo: () => ipcRenderer.invoke('wifi:get-current'),
  
  // 人脸识别相关操作

  
  // 指静脉识别相关操作
  // initializeFingerVein: () => ipcRenderer.invoke('finger-vein:initialize'),
  // captureFingerVein: (handle) => ipcRenderer.invoke('finger-vein:capture', { handle }),
  // verifyFingerVein: (handle, template) => ipcRenderer.invoke('finger-vein:verify', { handle, template }),
  // releaseFingerVein: (handle) => ipcRenderer.invoke('finger-vein:release', { handle }),
  
  // 虚拟键盘操作
  openOsk: () => ipcRenderer.invoke('open-osk'),
  closeOsk: () => ipcRenderer.invoke('close-osk')
})

// 为了兼容性，暴露electron对象
contextBridge.exposeInMainWorld('electron', {
  ipcRenderer: {
    invoke: (channel, ...args) => ipcRenderer.invoke(channel, ...args),
    send: (channel, ...args) => ipcRenderer.send(channel, ...args),
    on: (channel, listener) => {
      ipcRenderer.on(channel, listener)
      return () => ipcRenderer.removeListener(channel, listener)
    },
    once: (channel, listener) => ipcRenderer.once(channel, listener)
  }
})

console.log('Preload script finished, electronAPI and electron should be available')