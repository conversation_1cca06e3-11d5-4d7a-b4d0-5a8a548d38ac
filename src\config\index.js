/**
 * 系统配置文件
 * 包含WebSocket、API等系统级配置
 */

export default {
  // WebSocket服务器配置
  websocket: {
    // WebSocket服务器地址
    //  url: 'ws://192.168.3.102:18666',
        // url: 'ws://192.168.3.102:18666',
        url: 'ws://127.0.0.1:18666',
    // WebSocket连接参数
    params: {
     
    },
    // 心跳间隔(毫秒)
    pingInterval: 10000,
    // 最大重连次数
    maxReconnectCount: 5
  },
  
  // 系统通用配置
  system: {
    // 系统名称
    name: '智能证件柜存取系统'
  }
} 