<!-- 代码已包含 CSS：使用 TailwindCSS , 安装 TailwindCSS 后方可看到布局样式效果 -->

<template>
  <div class="bg-grid min-h-screen w-full flex flex-col tech-grid">
    <!-- 头部区域 -->
    <div class="header-fixed-area">
      <!-- 返回按钮 -->
      <div class="home-button-container">
        <HomeButton v-if="roleType !== 1" />
        <BackButton v-else button-text="返回上一页" />
      </div>
          <!--查询条件-->
          <div class="search-container">
        <div class="search-input-wrapper">
          <i class="el-icon-search search-icon"></i>
          <input 
            type="text" 
            v-model="realName" 
            placeholder="请输入姓名" 
            class="search-input"
            @keyup.enter="handleSearch"
          />
          <el-button
            type="primary"
            icon="el-icon-search"
            @click="handleSearch"
            class="search-btn"
          >查询</el-button>
        </div>
      </div>
       <!--查询条件-->

      <!-- 标题和操作按钮区 -->
      <div class="top-actions-container">
        <div class="left-actions">
       

          <el-button
            type="primary"
            icon="el-icon-plus"
            @click="handleAdd"
            class="new-user-btn"
            >新建</el-button
          >

          <el-button
            type="primary"
            icon="el-icon-plus"
            @click="handleImport"
            class="new-user-btn"
            >导入用户信息</el-button
          >
        </div>
        <div class="right-actions">
        
          <!--分页信息-->
          <div class="pagination-info">
            <span>当前第 {{ currentPage }} 页</span>
            <span class="divider">|</span>
            <span>共 {{ Math.ceil(totalItems / pageSize) || 0 }} 页</span>
            <span class="divider">|</span>
            <span>总计 {{ totalItems }} 条</span>
          </div>
        </div>
      </div>
    </div>

    <!-- 主要内容区 -->
    <div class="main-content-wrapper">
      <div class="content-container flex-1 flex flex-col px-2 pb-2">
        <!--卡片列表容器-->
        <div
          class="main-content-container"
        >
          <!-- 卡片列表替代表格 -->
          <div 
            class="card-list-container flex-1 overflow-auto" 
            ref="cardListContainer"
            @scroll="handleScroll"
          >
            <div class="card-grid">
              <div 
                v-for="(item, index) in displayedData" 
                :key="index"
                class="user-card"
              >
                <div class="user-card-content">
                  <div class="info-row">
                    <div class="info-label">用户名：</div>
                    <div class="info-value">{{ item.account }}</div>
                  </div>
                  <div class="info-row">
                    <div class="info-label">姓名：</div>
                    <div class="info-value">{{ item.realName || '张三' }}</div>
                  </div>
                  <div class="info-row">
                    <div class="info-label">手机号：</div>
                    <div class="info-value">{{ item.mobile }}</div>
                  </div>
                  <div class="info-row">
                    <div class="info-label">公司：</div>
                    <div class="info-value">{{ item.companyName }}</div>
                  </div>
                  <div class="info-row">
                    <div class="info-label">所属部门：</div>
                    <div class="info-value">{{ item.belongDeptName }}</div>
                  </div>
                  <div class="info-row">
                    <div class="info-label">工作部门：</div>
                    <div class="info-value">{{ item.workDeptName }}</div>
                  </div>
                  <div class="info-row">
                    <div class="info-label">是否自由存取：</div>
                    <div class="info-value">{{ item.isFreeAccess === '1' ? "是" : "否" }}</div>
                  </div>
                </div>

                <div class="user-card-actions">
                  <el-button
                    type="primary"
                    class="action-btn edit-btn"
                    @click="handleEdit(item)"
                    >编辑</el-button
                  >
                  <el-button
                    type="primary"
                    class="action-btn collect-btn"
                    @click="handleGenerate(item)"
                    >生物采集</el-button
                  >
                  <el-button
                    type="primary"
                    class="action-btn delete-btn"
                    @click="handleDelete(item)"
                    >删除</el-button
                  >
                </div>
              </div>
            </div>
            
            <!-- 加载更多指示器 -->
            <div v-if="isLoading" class="loading-indicator">
              <i class="el-icon-loading"></i>
              <span>加载中...</span>
            </div>
            <!-- 全部加载完毕提示 -->
            <div v-if="allDataLoaded && displayedData.length > 0" class="load-all-indicator">
              <span>——— 已加载全部数据 ———</span>
            </div>
            <!-- 没有数据提示 -->
            <div v-if="allDataLoaded && displayedData.length === 0" class="no-data-indicator">
              <i class="el-icon-warning-outline"></i>
              <span>暂无数据</span>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 添加/编辑用户对话框 组件 -->
    <user-dialog
      :dialog-title="dialogTitle"
      :dialog-visible.sync="dialogVisible"
      :group-data="form"
      :organizationtreedata="organizationtreedata"
      :role-options="roleOptions"
      :is-create-admin-mode="roleType === 1"
      :is-edit-mode="isEditMode"
      @submit="onDialogSubmit"
    ></user-dialog>

    <!-- 重置密码对话框组件 -->
    <reset-password-dialog
      :dialog-visible="resetPasswordVisible"
      :user-data="currentUser"
      @update:dialogVisible="(val) => (resetPasswordVisible = val)"
      @submit="onResetPasswordSubmit"
    ></reset-password-dialog>

    <!-- 删除确认对话框 -->
    <el-dialog
      title="确认删除"
      :visible.sync="deleteDialogVisible"
      width="30%"
      custom-class="delete-dialog"
    >
      <span
        >确定要删除用户 "{{ deleteItem.realName }}"
        删除用户后将同步销毁生物数据，是否删除该用户？</span
      >
      <span slot="footer" class="dialog-footer">
        <el-button @click="deleteDialogVisible = false">取 消</el-button>
        <el-button type="danger" @click="confirmDelete">确 定</el-button>
      </span>
    </el-dialog>

    <!-- 解锁确认对话框 -->
    <el-dialog
      title="确认解锁"
      :visible.sync="unlockDialogVisible"
      width="30%"
      custom-class="delete-dialog"
    >
      <span
        >确定要解锁用户 "{{ unlockItem.realName }}"
        吗？解锁后该用户状态为启动状态，是否解锁该用户？</span
      >
      <span slot="footer" class="dialog-footer">
        <el-button @click="unlockDialogVisible = false">取 消</el-button>
        <el-button type="primary" @click="confirmUnlock">确 定</el-button>
      </span>
    </el-dialog>

    <!-- 导入用户对话框 -->
    <el-dialog
      title="导入用户信息"
      :visible.sync="importDialogVisible"
      width="40%"
      custom-class="import-dialog"
    >
      <div class="import-container">
        <div class="import-btn-container text-center mb-4">
          <el-button
            type="primary"
            @click="selectImportFile"
            class="import-select-btn"
          >
            <i class="el-icon-folder-opened mr-2"></i>选择文件
          </el-button>
          <div v-if="selectedFilePath" class="selected-file-path mt-3">
            <p class="text-truncate">当前选择: {{ selectedFilePath }}</p>
          </div>
        </div>

        <div class="import-tips mt-4">
          <p class="text-warning mb-2">
            <i class="el-icon-warning mr-1"></i>导入说明：
          </p>
          <ul class="ml-4">
            <li>1. 请选择Excel文件(.xlsx/.xls)</li>
            <li>2. 文件必须包含用户名、姓名、手机号等必要字段</li>
            <li>3. 导入数据将仅在本地存储，连接网络后将自动同步</li>
            <li>
              4.路径不能用中文
            </li>
          </ul>
        </div>
      </div>
      <span slot="footer" class="dialog-footer">
        <el-button @click="importDialogVisible = false">取 消</el-button>
        <el-button
          type="primary"
          @click="importUsers"
          :disabled="!selectedFilePath"
          >导 入</el-button
        >
      </span>
    </el-dialog>

    <!-- 回到顶部按钮 -->
    <div 
      class="scroll-to-top-btn"
      :class="{ 'visible': showScrollTopBtn }"
      @click="scrollToTop"
    >
      <i class="el-icon-arrow-up"></i>
    </div>
  </div>
</template>

<script>
import userDialog from "./components/userDialog.vue";
import resetPasswordDialog from "./components/resetPasswordDialog.vue";
import HomeButton from "@/components/HomeButton.vue";
import BackButton from "@/components/BackButton.vue";
import storage from "@/utils/storage";
export default {
  name: "UserManageIndex",
  inject: ["speak", "websocketService", "websocketMsg"],
  watch: {
    websocketMsg: {
      handler(newMessage) {
        console.log("收到新的用户列表消息存证消息:", newMessage);
        if (newMessage) {
          this.handleUserListMessages(newMessage);
        }
      },
      deep: true,
      immediate: true,
    },
  },
  components: {
    userDialog,
    resetPasswordDialog,
    HomeButton,
    BackButton,
  },
  data() {
    return {
      roleOptions: [],
      organizationtreedata: [],
      searchText: "",
      currentPage: 1,
      pageSize: 20,
      totalItems: 0,
      dialogTitle: "",
      dialogVisible: false,
      deleteDialogVisible: false,
      deleteItem: {},
      // 导入用户相关
      importDialogVisible: false,
      fileList: [],
      selectedFile: null,
      selectedFilePath: "",
      form: {
        account: "",
        realName: "",
        mobile: "",
        companyName: "",
        belongDeptName: "",
        workDeptName: "",
        status: "1",
        gender: "1",
      },
      // 重置密码相关
      resetPasswordVisible: false,
      currentUser: {},
      // 表格数据
      tableData: [],
      displayedData: [], // 实际显示的数据
      // 解锁对话框
      unlockDialogVisible: false,
      unlockItem: {},
      // 滚动加载相关
      isLoading: false,
      allDataLoaded: false,
      scrollThreshold: 100, // 距离底部多少像素开始加载
      // 滚动到顶部按钮相关
      showScrollTopBtn: false,
      // 搜索条件
      realName: "",
      // 保存原始数据
      originalData: [],
      // 页面模式：normal-普通用户管理，createAdmin-创建管理员模式
      pageMode: "normal",
      // 超级管理员角色ID
      superAdminRoleId: null,
      // 角色类型过滤参数
      roleType: null,
      // 是否为编辑模式
      isEditMode: false,
    };
  },
  computed: {},
  created() {
    // 获取路由查询参数
    if (this.$route.query.roleType) {
      this.roleType = parseInt(this.$route.query.roleType);
    }

    this.getOrganizationtreedata();
    this.getRoleOptions();
    this.getData();
  },
  mounted() {
    // 检查屏幕方向并监听变化
    this.checkScreenOrientation();
    window.addEventListener('resize', this.checkScreenOrientation);
    
    // 监听滚动事件以显示/隐藏回到顶部按钮
    this.$nextTick(() => {
      if (this.$refs.cardListContainer) {
        this.$refs.cardListContainer.addEventListener('scroll', this.toggleScrollTopButton);
      }
    });
  },
  beforeDestroy() {
    window.removeEventListener('resize', this.checkScreenOrientation);
    
    // 移除滚动事件监听
    if (this.$refs.cardListContainer) {
      this.$refs.cardListContainer.removeEventListener('scroll', this.toggleScrollTopButton);
    }
  },
  methods: {
    // 处理搜索功能
    handleSearch() {
      // 重置页码并获取数据
      this.currentPage = 1;
      this.displayedData = [];
      this.tableData = [];
      this.allDataLoaded = false;
      this.getData();
    },
    
    // 检查屏幕方向，调整卡片网格布局
    checkScreenOrientation() {
      // 布局会通过CSS媒体查询自动调整
      console.log("Screen orientation changed or initialized");
    },
    
    // 处理滚动加载更多
    handleScroll() {
      if (this.isLoading || this.allDataLoaded) return;
      
      const container = this.$refs.cardListContainer;
      if (!container) return;
      
      const scrollPosition = container.scrollTop + container.clientHeight;
      const scrollHeight = container.scrollHeight;
      
      if (scrollHeight - scrollPosition <= this.scrollThreshold) {
        this.loadMoreData();
      }
    },
    
    // 加载更多数据
    loadMoreData() {
      if (this.isLoading || this.allDataLoaded) return;

      this.isLoading = true;

      // 增加页码并请求服务器数据
      this.currentPage++;

      const requestData = {
        current: this.currentPage,
        limit: this.pageSize,
        realName: this.realName
      };

      // 如果有 roleType 参数，添加到请求数据中
      if (this.roleType !== null) {
        requestData.roleType = this.roleType;
      }

      const order = {
        type: 10090,
        id: "getUserData",
        data: requestData,
      };

      // 发送请求获取下一页数据
      this.websocketService.send(order);
      // 数据加载和合并在handleUserListMessages的20090消息处理中完成
    },
    
    // 处理用户列表消息
    handleUserListMessages(newMessage) {
      console.log("收到新的用户列表消息存证消息:", newMessage);

      if (!newMessage) return;
      // 根据消息类型处理不同的情况
      if (newMessage && newMessage.messages) {
        switch (newMessage.messages.type) {
          case 20092: //获取角色列表
            if (
              newMessage.messages.data &&
              newMessage.messages.data.code === 1
            ) {
              console.log("角色列表数据:", newMessage.messages.data);
              this.roleOptions = newMessage.messages.data.list;
            } else {
              this.speak(newMessage.messages.data.msg);
            }
            break;
          case 20080: //获取组织树
            if (
              newMessage.messages.data &&
              newMessage.messages.data.code === 1
            ) {
              console.log("组织树数据:", newMessage.messages.data);
              this.organizationtreedata = newMessage.messages.data.data;
            } else {
              this.speak(newMessage.messages.data.msg);
            }
            break;
          case 20090: //用户管理分页
            this.isLoading = false;
            if (
              newMessage.messages.data &&
              newMessage.messages.data.code === 1
            ) {
              const records = newMessage.messages.data.records || [];
              this.totalItems = newMessage.messages.data.total;
              
              if (this.currentPage === 1) {
                // 首次加载或刷新
                this.tableData = records;
                this.displayedData = records;
                this.allDataLoaded = records.length === 0;
                // 清空搜索条件
                // this.account = "";
              } else if (records.length > 0) {
                // 追加数据到显示列表
                this.tableData = [...this.tableData, ...records];
                this.displayedData = [...this.displayedData, ...records];
              } else {
                // 没有更多数据了
                this.allDataLoaded = true;
              }

              // 判断是否已加载所有数据
              if (this.displayedData.length >= this.totalItems) {
                this.allDataLoaded = true;
              }
            } else {
              this.speak(newMessage.messages.data.msg);
            }
            break;

          case 20091: // 添加用户响应
            if (
              newMessage.messages.data &&
              newMessage.messages.data.code === 1
            ) {
              this.speak(
                "用户添加成功！当前为离线模式，数据仅本地存储，联网后用户信息将自动同步平台!"
              );
              this.$message({
                type: "success",
                message:
                  "用户添加成功！当前为离线模式，数据仅本地存储，联网后用户信息将自动同步平台!",
              });
              //
              this.getData();
              this.dialogVisible = false;
            } else {
              this.speak(newMessage.messages.data.msg);
            }

            break;

          case 20095: // 重置密码
            this.speak(
              "用户密码重置成功！当前为离线模式，数据仅本地存储，联网后用户信息将自动同步平台!"
            );
            this.$message({
              type: "success",
              message:
                "用户密码重置成功！当前为离线模式，数据仅本地存储，联网后用户信息将自动同步平台!",
            });
            this.getData();
            this.resetPasswordVisible = false;
            break;

          case 20093: // 删除用户响应
            if (
              newMessage.messages.data &&
              newMessage.messages.data.code === 1
            ) {
              this.speak(
                "用户删除成功！当前为离线模式，数据仅本地存储，联网后用户信息将自动同步平台!"
              );
              this.$message({
                type: "success",
                message:
                  "用户删除成功！当前为离线模式，数据仅本地存储，联网后用户信息将自动同步平台!",
              });
              this.getData();
              this.deleteDialogVisible = false;
            } else {
              this.$message({
                type: "error",
                message:newMessage.messages.data.msg,
              });
              this.speak(newMessage.messages.data.msg);
              this.deleteDialogVisible = false;
            }

            // 这里可以添加API调用，更新数据库
            break;

          case 20094: // 解锁用户响应
            if (
              newMessage.messages.data &&
              newMessage.messages.data.code === 1
            ) {
              this.speak(
                "用户解锁成功！当前为离线模式，数据仅本地存储，联网后用户信息将自动同步平台!"
              );
              this.$message({
                type: "success",
                message:
                  "用户解锁成功！当前为离线模式，数据仅本地存储，联网后用户信息将自动同步平台!",
              });
              this.getData();
              this.unlockDialogVisible = false;
            } else {
              this.speak(newMessage.messages.data.msg);
            }

            break;

          case 20075: // 导入用户响应
            this.speak(
              "用户数据导入成功！当前为离线模式，数据仅本地存储，联网后用户信息将自动同步平台!"
            );
            this.$message({
              type: "success",
              message:
                "用户数据导入成功！当前为离线模式，数据仅本地存储，联网后用户信息将自动同步平台!",
            });
            // 如果后端返回了导入的用户数据，可以在这里更新表格
            this.getData();
            break;

          case 20097: // 导入用户响应
            if (
              newMessage.messages.data &&
              newMessage.messages.data.code === 1
            ) {

              var msgString = newMessage?.messages?.data?.failedUsers ? newMessage.messages.data.failedUsers.map(item => item.error) : []
              if(msgString.length === 0){
                this.$message({
                type: "success",
                message:"导入成功",
              });
              }else{
                this.$message({
                type: "error",
                message:msgString.join(","),
              });
              }
          
              this.$loading().close();
              this.importDialogVisible = false;
              // 如果后端返回了导入的用户数据，可以在这里更新表格
              this.getData();
            } else {
              this.speak(newMessage.messages.data.msg);
              this.$loading().close();
              this.importDialogVisible = false;
            }
            break;

          default:
          // console.log("未处理的消息类型:", newMessage);
        }
      }
    },

    getOrganizationtreedata() {
      //获取组织树
      const order = {
        type: 10080,
        id: "getOrganizationtreedata",
        data: { parentId: 0 },
      };
      this.websocketService.send(order);
    },
    getRoleOptions() {
      //获取角色选项
      const order = {
        type: 10092,
        id: "getRoleOptions",
      };
      this.websocketService.send(order);
    },

    // 添加用户
    handleAdd() {
      this.isEditMode = false; // 设置为新增模式
      this.dialogTitle = "新增用户";
      // 重置表单
      this.form = {
        account: "",
        realName: "",
        mobile: "",
        companyId: "",
        belongDeptId: "",
        workDeptId: "",
        status: "1",
        gender: "1",
        isFreeAccess: '0',
        roleIds: [],
      };

      // 如果是创建管理员模式，设置超级管理员角色
      if (this.roleType === 1) {
        this.dialogTitle = "新增管理员";
        const superAdminRole = this.roleOptions.find(role =>
          role.roleName === '超级管理员' ||
          role.roleCode === 'super_admin' ||
          role.roleCode === 'SUPER_ADMIN' ||
          role.roleName.includes('超级管理员')
        );
        if (superAdminRole) {
          this.form.roleIds = [superAdminRole.id];
        }
      }

      this.dialogVisible = true;
    },

    // 返回首页
    goHome() {
      this.$router.push("/");
    },
    // 更新总条目数
    getData() {
      this.currentPage = 1;
      this.displayedData = [];
      this.tableData = [];
      this.allDataLoaded = false;

      const requestData = {
        current: this.currentPage,
        limit: this.pageSize,
        realName: this.realName
      };

      // 如果有 roleType 参数，添加到请求数据中
      if (this.roleType !== null) {
        requestData.roleType = this.roleType;
      }

      const order = {
        type: 10090,
        id: "getUserData",
        data: requestData,
      };
      this.websocketService.send(order);
    },

    // 班组状态改变
    handleStatusChange(row) {
      this.$message({
        type: "success",
        message: `${row.realName}的班组模式已${
          row.isEnabled ? "开启" : "关闭"
        }`,
      });
      // 这里可以添加API调用，更新数据库
    },
    // 编辑用户
    handleEdit(row) {
      this.isEditMode = true; // 设置为编辑模式
      this.dialogTitle = "编辑用户";
      // 复制当前行数据到表单
      this.form = JSON.parse(JSON.stringify(row));
      this.form.belongDeptId = row.belongDeptId + "";
      this.form.workDeptId = row.workDeptId + "";
      this.form.companyId = row.companyId + "";
      this.form.status = row.status + "";
      this.form.gender = row.gender + "";
      this.form.roleIds = row.roleIds;
      this.dialogVisible = true;
    },
    handleGenerate(row) {
      storage.local.set("currsorEditUser", {
        ...row,
      });
      this.$router.push({ path: "/collectMethod" });
    },
    // 删除用户
    handleDelete(row) {
      this.deleteItem = row;
      this.deleteDialogVisible = true;
    },
    // 确认删除
    confirmDelete() {
      let order = {
        type: 10093,
        id: "deleteUser",
        data: {
          id: this.deleteItem.id,
        },
      };
      this.websocketService.send(order);
      // 从数组中删除选中项
    },
    // 分页大小改变
    handleSizeChange(newSize) {
      this.pageSize = newSize;
      this.getData();
    },
    // 页码改变
    handleCurrentChange(newPage) {
      this.currentPage = newPage;
      this.getData();
    },
    // 处理对话框提交
    onDialogSubmit(formData) {
      let order = {
        type: 10091,
        id: "addUser",
        data: formData,
      };
      this.websocketService.send(order);
    },
    // 关闭班组模式
    handleCloseGroupMode(row) {
      this.$confirm(`确定要关闭 "${row.realName}" 的班组模式吗?`, "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(() => {
          row.isEnabled = false;
          this.$message({
            type: "success",
            message: `${row.realName} 的班组模式已关闭`,
          });
          // 这里可以添加API调用，更新数据库
        })
        .catch(() => {
          this.$message({
            type: "info",
            message: "已取消操作",
          });
        });
    },
    // 重置密码
    handleResetPassword(row) {
      this.currentUser = row;
      this.resetPasswordVisible = true;
    },
    // 处理重置密码提交
    onResetPasswordSubmit(data) {
      // 这里添加重置密码的逻辑，通常是调用API
      let order = {
        type: 10095,
        id: "resetPassword",
        data,
      };
      this.websocketService.send(order);
    },
    // 解锁
    handleUnlock(row) {
      this.unlockItem = row;
      this.unlockDialogVisible = true;
    },
    // 确认解锁
    confirmUnlock() {
      // 更新状态
      let order = {
        type: 10094,
        id: "unlockUser",
        data: {
          ids: [this.unlockItem.id],
        },
      };
      this.websocketService.send(order);
      // 这里可以添加API调用，更新数据库
    },
    // 打开导入对话框
    handleImport() {
      this.importDialogVisible = true;
      this.fileList = [];
      this.selectedFile = null;
      this.selectedFilePath = "";
    },
    // 选择导入文件
    selectImportFile() {
      // 使用Electron的dialog模块选择文件
      if (window.electronAPI) {
        window.electronAPI
          .selectImportFile({
            title: "选择用户数据文件",
            filters: [
              { name: "Excel文件", extensions: ["xlsx", "xls"] },
              // { name: 'CSV文件', extensions: ['csv'] },
              // { name: '所有文件', extensions: ['*'] }
            ],
            properties: ["openFile"],
          })
          .then((result) => {
            if (
              !result.canceled &&
              result.filePaths &&
              result.filePaths.length > 0
            ) {
              this.selectedFilePath = result.filePaths[0];
              console.log("选择的文件路径:", this.selectedFilePath);
            }
          })
          .catch((err) => {
            console.error("选择文件出错:", err);
            this.$message.error("选择文件失败，请重试");
          });
      } else {
        console.error("无法访问Electron API");
        this.$message.error("当前环境不支持文件选择功能");
      }
    },
    // 导入用户
    importUsers() {
      if (!this.selectedFilePath) {
        this.$message.warning("请先选择要导入的文件");
        return;
      }

      // 显示加载中
      this.$loading({
        lock: true,
        text: "正在提交导入请求，请稍候...",
        spinner: "el-icon-loading",
        background: "rgba(0, 0, 0, 0.7)",
      });

      // 发送文件路径到后端
      let order = {
        type: 10097,
        id: "importUsers",
        data: {
          path: this.selectedFilePath,
        },
      };

      this.websocketService.send(order);

      // 关闭加载提示
    },
    // 下载模板
    downloadTemplate() {
      // 模拟下载模板文件
      this.$message({
        type: "info",
        message: "模板下载中...",
      });

      // 实际项目中，这里应该实现一个真实的文件下载功能
      setTimeout(() => {
        this.$message({
          type: "success",
          message: "模板下载成功！",
        });
      }, 1000);
    },
    // 显示/隐藏回到顶部按钮
    toggleScrollTopButton() {
      const container = this.$refs.cardListContainer;
      if (!container) return;
      
      // 当滚动超过200px时显示按钮
      this.showScrollTopBtn = container.scrollTop > 200;
    },
    
    // 滚动到顶部
    scrollToTop() {
      const container = this.$refs.cardListContainer;
      if (!container) return;
      
      container.scrollTo({
        top: 0,
        behavior: 'smooth'
      });
    },
  },
};
</script>

<style scoped>
.bg-grid {
  background-color: #1c1f37;
  background-image: linear-gradient(
      rgba(255, 255, 255, 0.05) 1px,
      transparent 1px
    ),
    linear-gradient(90deg, rgba(255, 255, 255, 0.05) 1px, transparent 1px);
  background-size: 20px 20px;
  display: flex;
  flex-direction: column;
  height: 100vh;
  overflow: hidden;
  padding-top: 10px;
}

/* 头部固定区域 */
.header-fixed-area {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  z-index: 20;
  padding-top: 0;
  background-color: #1c1f37;
  background-image: linear-gradient(
      rgba(255, 255, 255, 0.05) 1px,
      transparent 1px
    ),
    linear-gradient(90deg, rgba(255, 255, 255, 0.05) 1px, transparent 1px);
  background-size: 20px 20px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.3);
}

.main-content-wrapper {
  margin-top: 165px; /* 头部区域的高度，根据实际调整 */
  flex: 1;
  overflow: hidden;
  display: flex;
  flex-direction: column;
  width: 100%;
 
}

/* 内容容器 */
.content-container {
  height: calc(100vh - 175px);
  overflow: hidden;
}

/* 卡片列表容器 */
.card-list-container {
  flex: 1;
  overflow-y: auto;
  padding: 0;
}

/* 卡片网格布局 */
.card-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
  gap: 20px;
  width: 100%;
  padding: 15px;
}

/* 横屏状态 - 三列布局 */
@media (min-width: 1200px) {
  .card-grid {
    grid-template-columns: repeat(3, 1fr);
  }
}

/* 平板状态 - 两列布局 */
@media (max-width: 1199px) and (min-width: 768px) {
  .card-grid {
    grid-template-columns: repeat(2, 1fr);
  }
}

/* 竖屏手机状态 - 单列布局 */
@media (max-width: 767px) {
  .card-grid {
    grid-template-columns: 1fr;
  }
}

/* 用户卡片样式 */
.user-card {
  display: flex;
  flex-direction: column;
  background-color: rgba(34, 44, 74, 0.8); /* 半透明深蓝色背景 */
  border-radius: 12px;
  overflow: hidden;
  box-shadow: 0 4px 10px rgba(0, 0, 0, 0.3);
  backdrop-filter: blur(5px); /* 添加模糊效果 */
  border: 1px solid rgba(255, 255, 255, 0.1); /* 添加细微边框增强效果 */
}

.user-card-content {
  padding: 15px 20px;
  flex-grow: 1;
}

/* 卡片信息区 */
.info-row {
  display: flex;
  margin-bottom: 10px;
  align-items: flex-start;
}

.info-label {
  min-width: 100px;
  color: rgba(255, 255, 255, 0.9);
  font-size: 14px;
  text-align: left;
}

.info-value {
  flex: 1;
  color: #ffffff;
  font-size: 14px;
  text-align: right;
  font-weight: 500;
}

/* 卡片操作区 */
.user-card-actions {
  display: flex;
  justify-content: space-between;
  padding: 10px;
  background-color: rgba(20, 27, 47, 0.6); /* 操作区域背景更透明 */
}

/* 按钮样式 */
.action-btn {
  padding: 8px 0;
  border-radius: 4px;
  color: white !important;
  border: none !important;
  font-size: 14px;
  width: 31%;
  margin: 0;
  line-height: 1.2;
  height: auto;
}

.edit-btn {
  background-color: #3f68cd !important;
}

.collect-btn {
  background-color: #3f68cd !important;
}

.delete-btn {
  background-color: #8c394a !important;
}

/* 加载更多指示器 */
.loading-indicator {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 60px;
  color: #fff;
  font-size: 14px;
  gap: 8px;
}

/* 全部加载完毕提示 */
.load-all-indicator {
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 20px 0;
  color: rgba(255, 255, 255, 0.6);
  font-size: 14px;
}

/* 没有数据提示 */
.no-data-indicator {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  padding: 40px 0;
  color: rgba(255, 255, 255, 0.6);
  gap: 10px;
}

.no-data-indicator i {
  font-size: 30px;
}

.no-data-indicator span {
  font-size: 14px;
}

/* 顶部操作区域样式 */
.top-actions-container {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 5px 20px 10px;
  width: 100%;
  margin-top: 15px; /* 为HomeButton留出空间 */
}

.left-actions {
  display: flex;
  align-items: center;
}

.right-actions {
  display: flex;
  align-items: center;
}

.new-user-btn {
  background-color: #3f68cd !important;
  border-color: #3f68cd !important;
  border-radius: 20px !important;
  padding: 8px 25px !important;
  font-size: 14px !important;
  height: auto !important;
  box-shadow: 0 2px 6px rgba(63, 104, 205, 0.5) !important;
}

.import-user-btn {
  color: rgba(255, 255, 255, 0.8) !important;
  font-size: 14px !important;
  padding: 0 !important;
}

.import-user-btn:hover {
  color: #fff !important;
  text-decoration: underline;
}

/* 页面标题调整为左对齐显示 */
.text-2xl {
  text-align: left;
  margin-left: 20px;
  margin-top: 15px;
  margin-bottom: 0;
}

/* 主内容容器 */
.main-content-container {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  background-color: rgba(26, 32, 53, 0.8);
  border-radius: 10px;
  box-shadow: 0 0 10px rgba(0, 0, 0, 0.2);
}

/* 对话框样式 */
.delete-dialog,
.import-dialog {
  border-radius: 4px !important;
  overflow: hidden !important;
}

:deep(.el-dialog) {
  background-color: #1c1f37 !important;
  border: 1px solid #263566 !important;
  border-radius: 4px !important;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.5) !important;
  overflow: hidden !important;
}

:deep(.el-dialog__header) {
  background-color: #263566 !important;
  padding: 15px 20px !important;
  border-bottom: 1px solid #263566 !important;
}

:deep(.el-dialog__title) {
  color: #fff !important;
  font-weight: bold !important;
}

:deep(.el-dialog__body) {
  color: #fff !important;
  padding: 20px !important;
}

.import-dialog :deep(.el-dialog__body) {
  padding: 0 !important;
}

:deep(.el-dialog__footer) {
  border-top: 1px solid #263566 !important;
  padding: 15px 20px !important;
}

:deep(.el-form-item__label) {
  color: #fff !important;
}

/* 导入对话框样式 */
.import-container {
  padding: 20px;
}

.import-tips {
  background-color: rgba(255, 229, 100, 0.1);
  border-left: 4px solid #e6a23c;
  padding: 10px 15px;
  border-radius: 0 4px 4px 0;
  color: #fff;
}

.text-warning {
  color: #e6a23c;
  font-weight: bold;
}

.import-select-btn {
  width: 180px;
}

.selected-file-path {
  color: #67c23a;
  word-break: break-all;
}

.text-truncate {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  max-width: 100%;
}

/* 按钮间距调整 */
.flex.space-x-2 {
  display: flex;
  gap: 16px; /* 增加按钮之间的间距 */
}

.add-user-btn {
  margin-right: 0; /* 重置按钮默认右边距 */
}

.home-button-container {
  position: absolute;
  top: 15px;
  right: 20px;
  z-index: 30;
}



/* 搜索条件样式 */
.search-container {
  display: flex;
  justify-content: center;
  padding: 10px 20px;
  background-color: #263566;
  border-radius: 8px;
  margin: 55px 0px 0px 0px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);
}

.search-input-wrapper {
  display: flex;
  align-items: center;
  background-color: #3a4a7a;
  border-radius: 6px;
  padding: 5px 10px;
  flex: 1;
  margin-right: 10px;
}

.search-icon {
  color: #8c96b3;
  font-size: 18px;
  margin-right: 8px;
}

.search-input {
  border: none;
  background-color: transparent;
  color: #fff;
  font-size: 14px;
  flex: 1;
  padding: 0;
  outline: none;
}

.search-btn {
  background-color: #3f68cd !important;
  border-color: #3f68cd !important;
  border-radius: 6px !important;
  padding: 8px 15px !important;
  font-size: 14px !important;
  height: auto !important;
  box-shadow: 0 2px 6px rgba(63, 104, 205, 0.5) !important;
}

/* 回到顶部按钮样式 */
.scroll-to-top-btn {
  position: fixed;
  bottom: 30px;
  right: 30px;
  width: 50px;
  height: 50px;
  border-radius: 50%;
  background-color: #3f68cd;
  color: white;
  display: flex;
  justify-content: center;
  align-items: center;
  cursor: pointer;
  z-index: 99;
  opacity: 0;
  visibility: hidden;
  transition: all 0.3s ease;
  box-shadow: 0 4px 10px rgba(0, 0, 0, 0.3);
}

.scroll-to-top-btn.visible {
  opacity: 1;
  visibility: visible;
}

.scroll-to-top-btn i {
  font-size: 24px;
}

.scroll-to-top-btn:hover {
  background-color: #2a4da6;
  transform: translateY(-3px);
  box-shadow: 0 6px 12px rgba(0, 0, 0, 0.4);
}

/* 分页信息样式 */
.pagination-info {
  display: flex;
  align-items: center;
  background-color: rgba(65, 105, 225, 0.2);
  border-radius: 20px;
  padding: 6px 15px;
  color: #fff;
  font-size: 14px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);
  margin-left: 15px;
}

.pagination-info .divider {
  margin: 0 8px;
  color: rgba(255, 255, 255, 0.5);
}
</style>

