import Vue from 'vue'
import App from './App.vue'
import store from './utils/store'
import router from './router'


// 引入 TailwindCSS
import './assets/tailwind.css'

// 引入Element UI
import ElementUI from 'element-ui'
import 'element-ui/lib/theme-chalk/index.css'
// 直接引入Element UI图标字体，解决打包后图标不显示问题
import 'element-ui/lib/theme-chalk/icon.css'
// 引入自定义Element UI主题
import './assets/element-theme.css'
// 引入自定义图标样式
import './assets/icons.css'

// 引入移动设备 Select 组件优化
import fixMobileSelect from './utils/mobile-select-fix'
// 引入自定义指令
import directives from './utils/directives'

// 引入Font Awesome
import '@fortawesome/fontawesome-free/css/all.css'
import '@fortawesome/fontawesome-free/js/all.js'

// 添加全局样式修复Element UI图标
const iconStyle = document.createElement('style')
iconStyle.innerHTML = `
@font-face {
  font-family: "element-icons";
  src: url("fonts/element-icons.woff") format("woff"),
       url("fonts/element-icons.ttf") format("truetype");
  font-weight: normal;
  font-style: normal;
}

[class^="el-icon-"], [class*=" el-icon-"] {
  font-family: "element-icons" !important;
}
`
document.head.appendChild(iconStyle)

Vue.config.productionTip = false

// 使用Element UI
Vue.use(ElementUI)

// 注册全局指令
Vue.directive('mobile-select', directives.mobileSelect);

// 添加到 Vue 原型链上
Vue.prototype.$store = store

new Vue({
  router,
  store,
  render: h => h(App),
  mounted() {
    // 应用启动后修复移动设备上的 Select 组件
    fixMobileSelect();
  }
}).$mount('#app')
