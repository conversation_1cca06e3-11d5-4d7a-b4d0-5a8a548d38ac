<!-- 返回按钮组件 -->
<template>
  <button
    class="text-white flex items-center space-x-2 !rounded-button whitespace-nowrap hover:opacity-80 cursor-pointer p-2"
    @click="goBack"
  >
    <i class="fas fa-arrow-left text-lg"></i>
    <span>{{ buttonText }}</span>
  </button>
</template>

<script>
export default {
  name: 'BackButton',
  props: {
    // 按钮文本
    buttonText: {
      type: String,
      default: '返回'
    },
    // 指定返回的路由名称，如果不指定则返回上一页
    routeName: {
      type: String,
      default: ''
    },
    // 路由参数
    routeParams: {
      type: Object,
      default: () => ({})
    },
    // 路由查询参数
    routeQuery: {
      type: Object,
      default: () => ({})
    }
  },
  data() {
    return {
      // 上次点击时间，用于防止快速连续点击
      lastClickTime: 0
    };
  },
  methods: {
    // 返回上一页或指定页面
    goBack() {
      const now = new Date().getTime();
      // 防止快速连续点击（300ms内）
      if (now - this.lastClickTime < 300) {
        console.log("点击过于频繁，忽略此次点击");
        return;
      }
      this.lastClickTime = now;
      
      // 发送点击事件
      this.$emit('click');
      
      // 如果指定了路由名称，则跳转到指定页面
      if (this.routeName) {
        this.$router.push({
          name: this.routeName,
          params: this.routeParams,
          query: this.routeQuery
        });
      } else {
        // 否则返回上一页
        this.$router.back();
      }
    }
  }
};
</script> 