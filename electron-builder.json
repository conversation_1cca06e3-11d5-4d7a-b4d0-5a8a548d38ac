{"appId": "com.example.icBox", "productName": "证件柜", "directories": {"output": "dist_electron"}, "files": ["dist_electron/bundled/**/*", "src/wsServer/**/*", "!src/wsServer/log/**/*", "!src/wsServer/*.log"], "extraResources": [{"from": "src/wsServer", "to": "wsServer", "filter": ["**/*", "!log/**/*", "!*.log"]}], "win": {"target": "nsis", "icon": "build/icons/icon.png"}, "nsis": {"oneClick": false, "allowToChangeInstallationDirectory": true, "perMachine": true, "allowElevation": true, "deleteAppDataOnUninstall": true, "createDesktopShortcut": true, "createStartMenuShortcut": true, "shortcutName": "证件柜", "displayLanguageSelector": true, "runAfterFinish": true, "installerIcon": "build/icons/icon.ico", "uninstallerIcon": "build/icons/icon.ico", "installerHeaderIcon": "build/icons/icon.ico"}, "portable": {"artifactName": "证件柜.exe"}, "linux": {"target": ["deb"], "icon": "build/icons", "category": "Utility", "maintainer": "证件柜开发团队 <<EMAIL>>", "executableName": "idcardbox", "synopsis": "智能证件柜管理系统", "vendor": "证件柜开发团队"}, "deb": {"artifactName": "证件柜_${version}_${arch}.deb", "depends": ["libgtk-3-0", "libnotify4", "libnss3", "libxss1", "xdg-utils"]}, "electronDownload": {"mirror": "https://cdn.npmmirror.com/binaries/"}, "buildDependenciesFromSource": true, "nodeGypRebuild": false, "npmRebuild": false, "remoteBuild": false}