<!-- 代码已包含 CSS：使用 TailwindCSS , 安装 TailwindCSS 后方可看到布局样式效果 -->

<template>
  <div class="bg-grid min-h-screen w-full flex flex-col relative tech-grid">
    <!-- 头部区域 -->
    <div class="header-fixed-area">
      <!-- 返回按钮 -->
      <div class="home-button-container">
        <HomeButton />
      </div>


               <!--查询条件-->
               <div class="search-container">
        <div class="search-input-wrapper">
          <i class="el-icon-search search-icon"></i>
          <input 
            type="text" 
            v-model="scheduleName" 
            placeholder="请输入班表名称" 
            class="search-input"
            @keyup.enter="handleSearch"
          />
          <el-button
            type="primary"
            icon="el-icon-search"
            @click="handleSearch"
            class="search-btn"
          >查询</el-button>
        </div>
      </div>

        <!--查询条件-->

      <!-- 标题和操作按钮区 -->
      <div class="top-actions-container">
        <div class="left-actions">
          <el-button
            type="primary"
            icon="el-icon-plus"
            @click="handleAdd"
            class="new-schedule-btn"
            >新增班表</el-button>
        </div>
        <div class="right-actions">
          <!--分页信息-->
          <div class="pagination-info">
            <span>当前第 {{ currentPage }} 页</span>
            <span class="divider">|</span>
            <span>共 {{ Math.ceil(totalItems / pageSize) || 0 }} 页</span>
            <span class="divider">|</span>
            <span>总计 {{ totalItems }} 条</span>
          </div>
        </div>
      </div>
    </div>

    <!-- 主要内容区 -->
    <div class="main-content-wrapper">
      <div class="content-container flex-1 flex flex-col px-2 pb-2">
        <!--卡片列表区域-->
        <div class="main-content-container">
          <!-- 卡片列表容器 添加滚动 -->
          <div class="card-list-container flex-1 overflow-auto" ref="cardListContainer" @scroll="handleScroll">
            <div class="card-grid">
              <div 
                v-for="(item, index) in displayedData" 
                :key="index"
                class="schedule-card"
              >
                <div class="schedule-card-content">
                  <div class="info-row">
                    <div class="info-label">班表编号：</div>
                    <div class="info-value">{{ item.scheduleCode }}</div>
                  </div>
                  <div class="info-row">
                    <div class="info-label">班表名称：</div>
                    <div class="info-value">{{ item.scheduleName }}</div>
                  </div>
                  <div class="info-row">
                    <div class="info-label">公司名称：</div>
                    <div class="info-value">{{ item.companyName }}</div>
                  </div>
                  <div class="info-row">
                    <div class="info-label">工作部门：</div>
                    <div class="info-value">{{ item.workDeptName }}</div>
                  </div>
                </div>

                <div class="schedule-card-actions">
                  <el-button
                    size="mini"
                    type="primary"
                    @click="handleManageSchedule(item)"
                    title="排班管理"
                    class="action-btn manage-btn"
                    >排班管理</el-button>
                  <el-button
                    size="mini"
                    type="primary"
                    @click="handleEdit(item)"
                    title="编辑"
                    class="action-btn edit-btn"
                    >编辑</el-button>
                  <el-button
                    size="mini"
                    type="danger"
                    @click="handleDelete(item)"
                    title="删除"
                    class="action-btn delete-btn"
                    >删除</el-button>
                </div>
              </div>
            </div>
            
            <!-- 加载更多指示器 -->
            <div v-if="isLoading" class="loading-indicator">
              <i class="el-icon-loading"></i>
              <span>加载中...</span>
            </div>
            
            <!-- 全部加载完毕提示 -->
            <div v-if="allDataLoaded && displayedData.length > 0" class="load-all-indicator">
              <span>——— 已加载全部数据 ———</span>
            </div>
            
            <!-- 没有数据提示 -->
            <div v-if="allDataLoaded && displayedData.length === 0" class="no-data-indicator">
              <i class="el-icon-warning-outline"></i>
              <span>暂无数据</span>
            </div>
          </div>
        </div>
      </div>
    </div>
    
    <!-- 回到顶部按钮 -->
    <div 
      class="scroll-to-top-btn"
      :class="{ 'visible': showScrollTopBtn }"
      @click="scrollToTop"
    >
      <i class="el-icon-arrow-up"></i>
    </div>
    
    <!-- 添加/编辑班表对话框 组件 -->
    <classSchedule
      :dialog-title="dialogTitle"
      :dialog-visible.sync="dialogVisible"
      :group-data="form"
      :organizationtreedata="organizationtreedata"
      @submit="onDialogSubmit"
    ></classSchedule>
    
    <!-- 排班管理组件 -->
    <scheduleManage
      :dialog-visible.sync="scheduleManageVisible"
      :schedule-info="currentSchedule"
    ></scheduleManage>
    
    <!-- 删除确认对话框 -->
    <el-dialog
      title="确认删除"
      :visible.sync="deleteDialogVisible"
      width="30%"
      custom-class="delete-dialog">
      <span>确定要删除班表 "{{ deleteItem.scheduleName }}" 吗？此操作不可恢复。</span>
      <span slot="footer" class="dialog-footer">
        <el-button @click="deleteDialogVisible = false">取 消</el-button>
        <el-button type="danger" @click="confirmDelete">确 定</el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
import classSchedule from './components/classSchedule.vue';
import scheduleManage from './components/scheduleManage.vue';
import HomeButton from "@/components/HomeButton.vue";
export default {
  name: 'SchedulingList',
  inject: ["speak", "websocketService", "websocketMsg", "speakSync"],
  watch: {
    websocketMsg: {
      handler(newMessage) {
        console.log("收到新的WebSocket消息班表管理消息:", newMessage);
        if (newMessage) {
          this.handleScheduleMessages(newMessage);
        }
      },
      deep: true,
      immediate: true,
    },
  },
  components: {
    classSchedule,
    scheduleManage,
    HomeButton
  },
  data() {
    return {
      organizationtreedata:[],
      searchText: '',
      currentPage: 1,
      pageSize: 20,
      totalItems: 0,
      dialogTitle: '',
      dialogVisible: false,
      deleteDialogVisible: false,
      deleteItem: {},
      form: {
        name: '',
        company: '',
        department: ''
      },
      
      // 当前选中的班表
      currentSchedule: {},
      // 排班管理对话框可见性
      scheduleManageVisible: false,
      // 表格数据
      tableData: [],
      displayedData: [], // 实际显示的数据
      isLoading: false,
      allDataLoaded: false,
      showScrollTopBtn: false,
      scrollThreshold: 100, // 距离底部多少像素开始加载
    };
  },

  created() {

  },
  mounted(){
    this.getOrganizationtreedata();
    this.getData();
    
    // 添加滚动事件监听
    this.$nextTick(() => {
      if (this.$refs.cardListContainer) {
        this.$refs.cardListContainer.addEventListener('scroll', this.toggleScrollTopButton);
      }
    });
  },
  
  beforeDestroy() {
    // 移除滚动事件监听
    if (this.$refs.cardListContainer) {
      this.$refs.cardListContainer.removeEventListener('scroll', this.toggleScrollTopButton);
    }
  },
  methods:{
    handleSearch() {
      // 重置页码并获取数据
      this.currentPage = 1;
      this.displayedData = [];
      this.tableData = [];
      this.allDataLoaded = false;
      this.getData();
    },
    handleScheduleMessages(message) {
      if (!message) return;
      // 根据消息类型处理不同的情况
      if (message && message.messages) {
        switch (message.messages.type) {
          case 20080: //获取组织树
            if (message.messages.data && message.messages.data.code === 1) {
              console.log("组织树数据:", message.messages.data);
              this.organizationtreedata = message.messages.data.data;
            } else {
              this.speak(message.messages.data.msg);
            }
            break;
          case 20096: //排班管理分页
            this.isLoading = false;
            if (message.messages.data && message.messages.data.code === 1) {
              const newData = message.messages.data.list || [];
              this.totalItems = message.messages.data.total;
              
              if (this.currentPage === 1) {
                // 首次加载或刷新
                this.tableData = newData;
                this.displayedData = newData;
              } else if (newData.length > 0) {
                // 追加数据
                this.tableData = [...this.tableData, ...newData];
                this.displayedData = [...this.displayedData, ...newData];
              }
              
              // 判断是否已加载所有数据
              this.allDataLoaded = this.tableData.length >= this.totalItems || newData.length === 0;
            } else {
              this.speak(message.messages.data.msg);
            }
            break;
          case 20061: //保存班组信息
            if (message.messages.data && message.messages.data.code === 1) {
              this.dialogVisible = false;
              this.resetAndReload();
              this.$message({
                type: "success",
                message:
                  "班表保存成功！当前为离线模式，数据仅本地存储，联网后用户信息将自动同步平台!",
              });
            } else {
              this.speak(message.messages.data.msg);
            }
            break;
          case 20062: //删除排班
            if (message.messages.data && message.messages.data.code === 1) {
              this.$message({
                type: "success",
                message:
                  "班表删除成功！当前为离线模式，数据仅本地存储，联网后用户信息将自动同步平台!",
              });
              this.deleteDialogVisible = false;
              this.resetAndReload();
            } else {
              this.speak(message.messages.data.msg);
            }
            break;
          default:
        
        }
      }
    },
    // 返回首页
    goHome() {
      this.$router.push('/');
    },
    getOrganizationtreedata() {
      //获取组织树
      const order = {
        type: 10080,
        id: "getOrganizationtreedata",
        data: { parentId: 0 },
      };
      this.websocketService.send(order);
    },
    // 重置并重新加载数据
    resetAndReload() {
      this.currentPage = 1;
      this.displayedData = [];
      this.tableData = [];
      this.allDataLoaded = false;
      this.getData();
      
      // 重置后自动滚动到顶部
      this.$nextTick(() => {
        this.scrollToTop();
      });
    },
    
    getData() {
      // 如果已经在加载或者已经加载完全部数据，则不再请求
      if (this.isLoading || (this.allDataLoaded && this.currentPage > 1)) return;
      
      this.isLoading = true;
      const order = {
        type: 10096,
        id: "getschedulingData",
        data: { current: this.currentPage, limit: this.pageSize, scheduleName: this.scheduleName },
      };
      this.websocketService.send(order);
    },
    // 搜索班表
    searchGroups() {
      // 实现搜索功能
      console.log('搜索班表:', this.searchText);
      this.resetAndReload();
    },
    // 添加班表
    handleAdd() {
      this.dialogTitle = '新增班表';
      // 重置表单
      this.form = {
        scheduleName: '',
        companyId: '',
        companyName: '',
        workDeptId: '',
        workDeptName: ''
      };
      this.dialogVisible = true;
    },
    // 编辑班表
    handleEdit(row) {
      this.dialogTitle = '编辑班表';
      // 复制当前行数据到表单
      var temp = JSON.parse(JSON.stringify(row));
      temp.companyId = temp.companyId + "";
      temp.workDeptId = temp.workDeptId + "";

      this.form = temp;
      this.dialogVisible = true;
    },
    // 删除班表
    handleDelete(row) {
      this.deleteItem = row;
      this.deleteDialogVisible = true;
    },
    // 确认删除
    confirmDelete() {
      // 从数组中删除选中项
      const order = {
        type: 10062,
        id: "delScheduling",
        data: { id: this.deleteItem.id },
      };
      this.websocketService.send(order);
    },
    // 分页大小改变
    handleSizeChange(newSize) {
      this.pageSize = newSize;
      this.resetAndReload();
    },
    // 页码改变
    handleCurrentChange(newPage) {
      this.currentPage = newPage;
      this.resetAndReload();
    },
    // 处理对话框提交
    onDialogSubmit(formData) {
      if (formData.id) {
        const order = {
          type: 10061,
          id: "editScheduling",
          data: { ...formData, id: formData.id ? formData.id + "" : "" },
        };
        this.websocketService.send(order);
      } else {
        // 添加新班表
        const order = {
          type: 10061,
          id: "addScheduling",
          data: { ...formData, id: formData.id ? formData.id + "" : "" },
        };
        this.websocketService.send(order);
      }
    },
    // 管理排班
    handleManageSchedule(row) {
      this.currentSchedule = row;
      this.scheduleManageVisible = true;
    },
    
    // 处理滚动事件，加载更多数据
    handleScroll() {
      const container = this.$refs.cardListContainer;
      if (!container) return;
      
      // 滚动到距离底部一定距离时，加载更多数据
      if (container.scrollTop + container.clientHeight >= container.scrollHeight - this.scrollThreshold) {
        if (!this.isLoading && !this.allDataLoaded) {
          this.currentPage++;
          this.getData();
        }
      }
      
      // 更新回到顶部按钮的可见性
      this.toggleScrollTopButton();
    },
    
    // 显示/隐藏回到顶部按钮
    toggleScrollTopButton() {
      const container = this.$refs.cardListContainer;
      if (container) {
        this.showScrollTopBtn = container.scrollTop > 200;
      }
    },
    
    // 滚动到顶部
    scrollToTop() {
      const container = this.$refs.cardListContainer;
      if (container) {
        container.scrollTo({
          top: 0,
          behavior: 'smooth'
        });
      }
    },
  }
};
</script>

<style scoped>
.bg-grid {
  background-color: #1c1f37;
  background-image: linear-gradient(
      rgba(255, 255, 255, 0.05) 1px,
      transparent 1px
    ),
    linear-gradient(90deg, rgba(255, 255, 255, 0.05) 1px, transparent 1px);
  background-size: 20px 20px;
  display: flex;
  flex-direction: column;
  height: 100vh;
  overflow: hidden;
  padding-top: 10px;
}

/* 头部固定区域 */
.header-fixed-area {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  z-index: 20;
  padding-top: 0;
  background-color: #1c1f37;
  background-image: linear-gradient(
      rgba(255, 255, 255, 0.05) 1px,
      transparent 1px
    ),
    linear-gradient(90deg, rgba(255, 255, 255, 0.05) 1px, transparent 1px);
  background-size: 20px 20px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.3);
}

.main-content-wrapper {
  margin-top: 165px; /* 头部区域的高度，根据实际调整 */
  flex: 1;
  overflow: hidden;
  display: flex;
  flex-direction: column;
  width: 100%;
}

/* 内容容器 */
.content-container {
  height: calc(100vh - 175px);
    overflow: hidden;
}

/* 主内容容器 */
.main-content-container {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  background-color: rgba(26, 32, 53, 0.8);
  border-radius: 10px;
  box-shadow: 0 0 10px rgba(0, 0, 0, 0.2);
}

/* 顶部操作区域样式 */
.top-actions-container {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 5px 20px 10px;
  width: 100%;
  margin-top: 15px; /* 为HomeButton留出空间 */
}

.left-actions {
  display: flex;
  align-items: center;
}

.right-actions {
  display: flex;
  align-items: center;
}

.home-button-container {
  position: absolute;
  top: 15px;
  right: 20px;
  z-index: 30;
}

.new-schedule-btn {
  background-color: #3f68cd !important;
  border-color: #3f68cd !important;
  border-radius: 20px !important;
  padding: 8px 25px !important;
  font-size: 14px !important;
  height: auto !important;
  box-shadow: 0 2px 6px rgba(63, 104, 205, 0.5) !important;
}

/* 卡片列表容器 */
.card-list-container {
  flex: 1;
  overflow-y: auto;
  padding: 0;
}

/* 卡片网格布局 */
.card-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
  gap: 20px;
  width: 100%;
  padding: 15px;
}

/* 横屏状态 - 三列布局 */
@media (min-width: 1200px) {
  .card-grid {
    grid-template-columns: repeat(3, 1fr);
  }
}

/* 平板状态 - 两列布局 */
@media (max-width: 1199px) and (min-width: 768px) {
  .card-grid {
    grid-template-columns: repeat(2, 1fr);
  }
}

/* 竖屏手机状态 - 单列布局 */
@media (max-width: 767px) {
  .card-grid {
    grid-template-columns: 1fr;
  }
}

/* 班表卡片样式 */
.schedule-card {
  display: flex;
  flex-direction: column;
  background-color: rgba(34, 44, 74, 0.8);
  border-radius: 12px;
  overflow: hidden;
  box-shadow: 0 4px 10px rgba(0, 0, 0, 0.3);
  backdrop-filter: blur(5px);
  border: 1px solid rgba(255, 255, 255, 0.1);
}

.schedule-card-content {
  padding: 15px 20px;
  flex-grow: 1;
}

/* 卡片信息区 */
.info-row {
  display: flex;
  margin-bottom: 12px;
  align-items: flex-start;
}

.info-label {
  min-width: 110px;
  color: rgba(255, 255, 255, 0.9);
  font-size: 14px;
  text-align: left;
}

.info-value {
  flex: 1;
  color: #ffffff;
  font-size: 14px;
  text-align: right;
  font-weight: 500;
}

/* 卡片操作区 */
.schedule-card-actions {
  display: flex;
  justify-content: space-between;
  padding: 10px;
  background-color: rgba(20, 27, 47, 0.6);
}

/* 按钮样式 */
.action-btn {
  padding: 8px 0;
  border-radius: 4px;
  color: white !important;
  border: none !important;
  font-size: 14px;
  width: 32%;
  margin: 0;
  line-height: 1.2;
  height: auto;
}

.manage-btn {
  background-color: #3f68cd !important;
}

.edit-btn {
  background-color: #3f68cd !important;
}

.delete-btn {
  background-color: #8c394a !important;
}

/* 没有数据提示 */
.no-data-indicator {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  padding: 40px 0;
  color: rgba(255, 255, 255, 0.6);
  gap: 10px;
}

.no-data-indicator i {
  font-size: 30px;
}

.no-data-indicator span {
  font-size: 14px;
}

/* 加载更多指示器 */
.loading-indicator {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 60px;
  color: #fff;
  font-size: 14px;
  gap: 8px;
}

/* 全部加载完毕提示 */
.load-all-indicator {
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 20px 0;
  color: rgba(255, 255, 255, 0.6);
  font-size: 14px;
}

/* 回到顶部按钮 */
.scroll-to-top-btn {
  position: fixed;
  bottom: 30px;
  right: 30px;
  width: 50px;
  height: 50px;
  border-radius: 50%;
  background-color: #4e77e5;
  color: white;
  display: flex;
  justify-content: center;
  align-items: center;
  cursor: pointer;
  z-index: 99;
  opacity: 0;
  visibility: hidden;
  transition: all 0.3s ease;
  box-shadow: 0 4px 10px rgba(0, 0, 0, 0.3);
}

.scroll-to-top-btn.visible {
  opacity: 1;
  visibility: visible;
}

.scroll-to-top-btn i {
  font-size: 24px;
}

.scroll-to-top-btn:hover {
  background-color: #3f68cd;
  transform: translateY(-3px);
  box-shadow: 0 6px 12px rgba(0, 0, 0, 0.4);
}

/* 对话框样式 */
.delete-dialog {
  border-radius: 4px !important;
  overflow: hidden !important;
}

:deep(.el-dialog) {
  background-color: #1c1f37 !important;
  border: 1px solid #263566 !important;
  border-radius: 4px !important;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.5) !important;
  overflow: hidden !important;
}

:deep(.el-dialog__header) {
  background-color: #263566 !important;
  padding: 15px 20px !important;
  border-bottom: 1px solid #263566 !important;
}

:deep(.el-dialog__title) {
  color: #fff !important;
  font-weight: bold !important;
}

:deep(.el-dialog__body) {
  color: #fff !important;
  padding: 20px !important;
}

:deep(.el-dialog__footer) {
  border-top: 1px solid #263566 !important;
  padding: 15px 20px !important;
}

:deep(.el-form-item__label) {
  color: #fff !important;
}

/* 分页信息样式 */
.pagination-info {
  display: flex;
  align-items: center;
  background-color: rgba(65, 105, 225, 0.2);
  border-radius: 20px;
  padding: 6px 15px;
  color: #fff;
  font-size: 14px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);
}

.pagination-info .divider {
  margin: 0 8px;
  color: rgba(255, 255, 255, 0.5);
}


/* 搜索条件样式 */
.search-container {
  display: flex;
  justify-content: center;
  padding: 10px 20px;
  background-color: #263566;
  border-radius: 8px;
  margin: 55px 0px 0px 0px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);
}

.search-input-wrapper {
  display: flex;
  align-items: center;
  background-color: #3a4a7a;
  border-radius: 6px;
  padding: 5px 10px;
  flex: 1;
  margin-right: 10px;
}

.search-icon {
  color: #8c96b3;
  font-size: 18px;
  margin-right: 8px;
}

.search-input {
  border: none;
  background-color: transparent;
  color: #fff;
  font-size: 14px;
  flex: 1;
  padding: 0;
  outline: none;
}

.search-btn {
  background-color: #3f68cd !important;
  border-color: #3f68cd !important;
  border-radius: 6px !important;
  padding: 8px 15px !important;
  font-size: 14px !important;
  height: auto !important;
  box-shadow: 0 2px 6px rgba(63, 104, 205, 0.5) !important;
}
</style>

