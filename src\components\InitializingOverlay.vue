<template>
  <transition name="fade">
    <div v-if="visible" class="initializing-overlay">
      <div class="overlay-container">
        <!-- 背景粒子效果 -->
        <div class="particles-container">
          <div v-for="n in 20" :key="n" class="particle"></div>
        </div>
        
        <div class="overlay-content">
          <!-- 品牌 Logo 区域 -->
          <div class="logo-container">
            <div class="logo-circle">
              <span class="logo-text">证件柜</span>
            </div>
          </div>
          
          <!-- 加载动画 -->
          <div class="loading-container">
            <div class="spinner">
              <svg viewBox="0 0 50 50" class="spinner-svg">
                <circle class="path" cx="25" cy="25" r="20" fill="none" stroke-width="4"></circle>
              </svg>
            </div>
          </div>
          
          <!-- 状态信息 -->
          <div class="status-container">
            <h2 class="overlay-title">正在初始化硬件</h2>
            <p class="overlay-message">请稍候<span class="dots">...</span></p>
            <div class="progress-bar">
              <div class="progress-inner"></div>
            </div>
          </div>
        </div>
        
        <!-- 底部版权信息 -->
        <div class="footer">
          <p>© 2025 证件柜系统 版权所有</p>
        </div>
      </div>
    </div>
  </transition>
</template>

<script>
export default {
  name: 'InitializingOverlay',
  props: {
    visible: {
      type: Boolean,
      default: false
    }
  }
}
</script>

<style scoped>
.initializing-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(135deg, #1a1f2c 0%, #121721 100%);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 9999;
}

.overlay-container {
  position: relative;
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  align-items: center;
  padding: 40px 20px;
  overflow: hidden;
}

/* 背景粒子动画 */
.particles-container {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  overflow: hidden;
  z-index: -1;
}

.particle {
  position: absolute;
  width: 6px;
  height: 6px;
  background: rgba(73, 131, 254, 0.15);
  border-radius: 50%;
  animation: float 15s infinite linear;
}

.particle:nth-child(odd) {
  background: rgba(73, 131, 254, 0.2);
}

.particle:nth-child(even) {
  width: 8px;
  height: 8px;
}

.particle:nth-child(3n) {
  width: 4px;
  height: 4px;
  background: rgba(73, 131, 254, 0.1);
}

@keyframes float {
  0% {
    transform: translateY(100vh) translateX(0);
    opacity: 0;
  }
  20% {
    opacity: 0.8;
  }
  50% {
    transform: translateY(50vh) translateX(20px);
  }
  80% {
    opacity: 0.6;
  }
  100% {
    transform: translateY(0) translateX(0);
    opacity: 0;
  }
}

/* 给每个粒子随机位置和动画延迟 */
.particle:nth-child(1) { left: 10%; animation-delay: 0s; }
.particle:nth-child(2) { left: 20%; animation-delay: 2s; }
.particle:nth-child(3) { left: 30%; animation-delay: 4s; }
.particle:nth-child(4) { left: 40%; animation-delay: 6s; }
.particle:nth-child(5) { left: 50%; animation-delay: 8s; }
.particle:nth-child(6) { left: 60%; animation-delay: 10s; }
.particle:nth-child(7) { left: 70%; animation-delay: 12s; }
.particle:nth-child(8) { left: 80%; animation-delay: 14s; }
.particle:nth-child(9) { left: 90%; animation-delay: 1s; }
.particle:nth-child(10) { left: 5%; animation-delay: 3s; }
.particle:nth-child(11) { left: 15%; animation-delay: 5s; }
.particle:nth-child(12) { left: 25%; animation-delay: 7s; }
.particle:nth-child(13) { left: 35%; animation-delay: 9s; }
.particle:nth-child(14) { left: 45%; animation-delay: 11s; }
.particle:nth-child(15) { left: 55%; animation-delay: 13s; }
.particle:nth-child(16) { left: 65%; animation-delay: 0.5s; }
.particle:nth-child(17) { left: 75%; animation-delay: 2.5s; }
.particle:nth-child(18) { left: 85%; animation-delay: 4.5s; }
.particle:nth-child(19) { left: 95%; animation-delay: 6.5s; }
.particle:nth-child(20) { left: 8%; animation-delay: 8.5s; }

.overlay-content {
  flex: 1;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  max-width: 600px;
  width: 100%;
}

/* Logo 样式 */
.logo-container {
  margin-bottom: 50px;
}

.logo-circle {
  width: 120px;
  height: 120px;
  border-radius: 50%;
  background: linear-gradient(135deg, #4983fe, #2b5cd9);
  display: flex;
  justify-content: center;
  align-items: center;
  box-shadow: 0 10px 30px rgba(73, 131, 254, 0.3);
  animation: pulse 2s infinite;
}

.logo-text {
  color: white;
  font-size: 28px;
  font-weight: bold;
  letter-spacing: 2px;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
}

@keyframes pulse {
  0% {
    box-shadow: 0 0 0 0 rgba(73, 131, 254, 0.6);
  }
  70% {
    box-shadow: 0 0 0 15px rgba(73, 131, 254, 0);
  }
  100% {
    box-shadow: 0 0 0 0 rgba(73, 131, 254, 0);
  }
}

/* 加载动画容器 */
.loading-container {
  margin-bottom: 40px;
}

/* 旋转加载动画 */
.spinner {
  width: 80px;
  height: 80px;
}

.spinner-svg {
  animation: rotate 2s linear infinite;
  transform-origin: center center;
  width: 100%;
  height: 100%;
  margin: auto;
}

.path {
  stroke: #4983fe;
  stroke-dasharray: 150, 200;
  stroke-dashoffset: -10;
  animation: dash 1.5s ease-in-out infinite;
  stroke-linecap: round;
}

@keyframes rotate {
  100% {
    transform: rotate(360deg);
  }
}

@keyframes dash {
  0% {
    stroke-dasharray: 1, 200;
    stroke-dashoffset: 0;
  }
  50% {
    stroke-dasharray: 89, 200;
    stroke-dashoffset: -35px;
  }
  100% {
    stroke-dasharray: 89, 200;
    stroke-dashoffset: -124px;
  }
}

/* 状态信息容器 */
.status-container {
  text-align: center;
}

.overlay-title {
  color: #ffffff;
  font-size: 28px;
  margin-bottom: 16px;
  font-weight: 500;
}

.overlay-message {
  color: #b8c2d0;
  font-size: 20px;
  margin-top: 8px;
  margin-bottom: 25px;
}

/* 进度条 */
.progress-bar {
  width: 300px;
  height: 6px;
  background-color: rgba(255, 255, 255, 0.1);
  border-radius: 3px;
  overflow: hidden;
  margin: 0 auto;
}

.progress-inner {
  height: 100%;
  width: 30%;
  background: linear-gradient(90deg, #4983fe, #60a5fa);
  border-radius: 3px;
  animation: progress 2s infinite;
  animation-timing-function: ease-in-out;
}

@keyframes progress {
  0% { width: 10%; }
  50% { width: 70%; }
  100% { width: 10%; }
}

/* 动画点点 */
.dots {
  display: inline-block;
  width: 30px;
  position: relative;
}

@keyframes dotAnimation {
  0% { content: '.'; }
  33% { content: '..'; }
  66% { content: '...'; }
  100% { content: ''; }
}

.dots::after {
  content: '';
  animation: dotAnimation 1.5s infinite;
}

/* 底部版权信息 */
.footer {
  padding: 20px 0;
  font-size: 14px;
  color: rgba(255, 255, 255, 0.4);
  text-align: center;
}

/* 淡入淡出动画 */
.fade-enter-active, .fade-leave-active {
  transition: opacity 0.8s;
}
.fade-enter, .fade-leave-to {
  opacity: 0;
}
</style> 