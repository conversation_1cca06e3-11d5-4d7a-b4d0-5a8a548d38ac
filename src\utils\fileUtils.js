const fs = require('fs')
const path = require('path')
const { dialog } = require('electron')

/**
 * 文件操作工具类
 */
class FileUtils {
  /**
   * 读取文件内容
   * @param {string} filePath 文件路径
   * @returns {Promise<string>} 文件内容
   */
  static readFile(filePath) {
    return new Promise((resolve, reject) => {
      fs.readFile(filePath, 'utf8', (err, data) => {
        if (err) {
          reject(err)
          return
        }
        resolve(data)
      })
    })
  }

  /**
   * 写入文件内容
   * @param {string} filePath 文件路径
   * @param {string} content 文件内容
   * @returns {Promise<void>}
   */
  static writeFile(filePath, content) {
    return new Promise((resolve, reject) => {
      fs.writeFile(filePath, content, 'utf8', (err) => {
        if (err) {
          reject(err)
          return
        }
        resolve()
      })
    })
  }

  /**
   * 选择文件
   * @param {Object} options 选项配置
   * @returns {Promise<string[]>} 选中的文件路径数组
   */
  static async selectFile(options = {}) {
    try {
      const defaultOptions = {
        properties: ['openFile'],
        filters: [
          { name: '所有文件', extensions: ['*'] }
        ]
      }
      const result = await dialog.showOpenDialog({
        ...defaultOptions,
        ...options
      })
      return result.filePaths
    } catch (error) {
      console.error('选择文件错误:', error)
      throw error
    }
  }
  
  /**
   * 选择用户导入文件
   * 专门用于处理用户文件导入
   * @param {Object} options 选项配置
   * @returns {Promise<Object>} 选择结果，包含filePaths和canceled属性
   */
  static async selectImportFile(options = {}) {
    try {
      const defaultOptions = {
        title: '选择用户数据文件',
        properties: ['openFile'],
        filters: [
          { name: 'Excel文件', extensions: ['xlsx', 'xls'] },
          { name: 'CSV文件', extensions: ['csv'] },
          { name: '所有文件', extensions: ['*'] }
        ]
      }
      return await dialog.showOpenDialog({
        ...defaultOptions,
        ...options
      })
    } catch (error) {
      console.error('选择导入文件错误:', error)
      throw error
    }
  }

  /**
   * 选择保存路径
   * @param {Object} options 选项配置
   * @returns {Promise<string>} 保存路径
   */
  static async selectSavePath(options = {}) {
    const defaultOptions = {
      properties: ['saveFile'],
      filters: [
        { name: '所有文件', extensions: ['*'] }
      ]
    }
    const result = await dialog.showSaveDialog({
      ...defaultOptions,
      ...options
    })
    return result.filePath
  }

  /**
   * 检查文件是否存在
   * @param {string} filePath 文件路径
   * @returns {boolean}
   */
  static exists(filePath) {
    return fs.existsSync(filePath)
  }

  /**
   * 创建目录
   * @param {string} dirPath 目录路径
   * @returns {Promise<void>}
   */
  static mkdir(dirPath) {
    return new Promise((resolve, reject) => {
      fs.mkdir(dirPath, { recursive: true }, (err) => {
        if (err) {
          reject(err)
          return
        }
        resolve()
      })
    })
  }
}

export default FileUtils 