<template>
  <div class="soft-keyboard-toggle">
    <!-- 软键盘切换按钮 -->
    <el-button
      @click="toggleKeyboard"
      :type="keyboardVisible ? 'danger' : 'primary'"
      size="medium"
      :icon="keyboardVisible ? 'el-icon-close' : 'el-icon-edit'"
      class="keyboard-toggle-btn"
      circle
    >
      {{ keyboardVisible ? '隐藏' : '显示' }}
    </el-button>
  </div>
</template>

<script>
export default {
  name: 'SoftKeyboard',
  props: {
    // 是否启用快捷键
    enableShortcuts: {
      type: Boolean,
      default: true
    },
    // 是否在初始化时自动显示软键盘
    autoInitialize: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      keyboardVisible: false
    }
  },
  mounted() {
    // 初始化快捷键监听
    if (this.enableShortcuts) {
      this.initShortcuts()
    }
    
    // 如果设置了自动初始化，则显示软键盘
    if (this.autoInitialize) {
      this.showKeyboard()
    }
  },
  beforeDestroy() {
    // 清理快捷键监听
    if (this.enableShortcuts) {
      this.removeShortcuts()
    }
    
    // 确保在组件销毁时隐藏软键盘
    if (this.keyboardVisible) {
      this.hideKeyboard()
    }
  },
  methods: {
    /**
     * 显示软键盘
     */
    showKeyboard() {
      try {
        if (!this.keyboardVisible) {
          // 调用Electron API显示软键盘
          if (window.electronAPI && window.electronAPI.openOsk) {
            window.electronAPI.openOsk()
            this.keyboardVisible = true
            this.$emit('keyboard-show')
            this.$emit('keyboard-change', true)
            
            console.log('软键盘显示成功')
          } else {
            throw new Error('Electron API不可用')
          }
        }
      } catch (error) {
        console.error('显示软键盘失败:', error)
        this.$message.error('显示软键盘失败: ' + error.message)
        this.$emit('keyboard-error', error)
      }
    },
    
    /**
     * 隐藏软键盘
     */
    hideKeyboard() {
      try {
        if (this.keyboardVisible) {
          // 调用Electron API隐藏软键盘
          if (window.electronAPI && window.electronAPI.closeOsk) {
            window.electronAPI.closeOsk()
            this.keyboardVisible = false
            this.$emit('keyboard-hide')
            this.$emit('keyboard-change', false)
            
            console.log('软键盘隐藏成功')
          } else {
            throw new Error('Electron API不可用')
          }
        }
      } catch (error) {
        console.error('隐藏软键盘失败:', error)
        this.$message.error('隐藏软键盘失败: ' + error.message)
        this.$emit('keyboard-error', error)
      }
    },
    
    /**
     * 切换软键盘显示状态
     */
    toggleKeyboard() {
      if (this.keyboardVisible) {
        this.hideKeyboard()
      } else {
        this.showKeyboard()
      }
    },
    
    /**
     * 初始化快捷键监听
     */
    initShortcuts() {
      this.shortcutHandler = (event) => {
        // Ctrl+K: 切换软键盘
        if (event.ctrlKey && event.key === 'k' && !event.shiftKey && !event.altKey) {
          event.preventDefault()
          this.toggleKeyboard()
        }
      }
      
      document.addEventListener('keydown', this.shortcutHandler)
    },
    
    /**
     * 移除快捷键监听
     */
    removeShortcuts() {
      if (this.shortcutHandler) {
        document.removeEventListener('keydown', this.shortcutHandler)
        this.shortcutHandler = null
      }
    }
  }
}
</script>

<style scoped>
.soft-keyboard-toggle {
  position: fixed;
  bottom: 130px;
  right: 30px;
  z-index: 9999;
}

.keyboard-toggle-btn {
  width: 60px;
  height: 60px;
  border-radius: 50%;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  transition: all 0.3s ease;
  font-size: 12px;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  gap: 2px;
}

.keyboard-toggle-btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 6px 20px rgba(0, 0, 0, 0.2);
}

.keyboard-toggle-btn i {
  font-size: 16px;
  margin: 0;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .soft-keyboard-toggle {
    bottom: 20px;
    right: 20px;
  }
  
  .keyboard-toggle-btn {
    width: 50px;
    height: 50px;
    font-size: 10px;
  }
  
  .keyboard-toggle-btn i {
    font-size: 14px;
  }
}

/* 动画效果 */
@keyframes fadeIn {
  from {
    opacity: 0;
    transform: scale(0.8);
  }
  to {
    opacity: 1;
    transform: scale(1);
  }
}

.soft-keyboard-toggle {
  animation: fadeIn 0.3s ease-out;
}
</style> 