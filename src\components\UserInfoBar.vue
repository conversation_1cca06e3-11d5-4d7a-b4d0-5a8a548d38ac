<!-- 用户信息栏组件 -->
<template>
  <div class="user-info-wrapper" :key="key">
   
    <!-- 使用新的网络状态栏组件 -->
   
    
    <!-- 原有的用户信息和时间信息行 -->
    <div class="user-info-bar flex items-center z-40">
      <div class="user-info flex items-center">
        <!-- 用户信息部分 -->
        <template v-if="displayUsername.length != 0">
          <i class="fas fa-user text-blue-300 mr-2" ></i>
          <span class="text-white">{{ displayUsername }}</span>
          <template v-if="displayRealName.length != 0">
            <span class="text-gray-400">({{ displayRealName }})</span>
          </template>
          <span class="mx-3 text-gray-400">|</span>
        </template>
        <!-- 时间信息部分 -->
        <i class="fas fa-clock text-blue-300 mr-2"></i>
        <span class="text-white">{{ currentTime }}</span>
      </div>
    </div>
  </div>
</template>

<script>
import storage from '@/utils/storage';
// 导入网络状态栏组件


export default {
  name: "UserInfoBar",
  components: {
    
  },
  // 移除网络状态相关的inject
  inject: ["speak"],
  props: {
    // 是否显示在绝对位置
    absolute: {
      type: Boolean,
      default: false
    },
    // 位置自定义
    position: {
      type: String,
      default: 'top-8 left-8'
    }
  },
  data() {
    return {
      // 当前时间
      currentTime: '',
      // 时间更新定时器
      timeInterval: null,
      // 本地存储的用户名
      key: 0,
      username: '',
      realName: '',
    };
  },
  created() {
    // 初始化时间
    this.updateCurrentTime();
    // 设置定时器，每秒更新一次时间
    this.timeInterval = setInterval(this.updateCurrentTime, 1000);
    // 从本地存储获取用户信息
    this.getUserInfoFromStore();
  },
  beforeDestroy() {
    // 组件销毁前清除定时器
    if (this.timeInterval) {
      clearInterval(this.timeInterval);
    }
  },
  computed: {
    // 计算最终显示的用户名，优先使用本地存储中的用户名
    displayUsername() {
      return this.username || '';
    },
    displayRealName() {
      return this.realName || '';
    }
  },
  methods: {
    // 更新当前时间
    updateCurrentTime() {
      const now = new Date();
      const year = now.getFullYear();
      const month = String(now.getMonth() + 1).padStart(2, '0');
      const day = String(now.getDate()).padStart(2, '0');
      const hours = String(now.getHours()).padStart(2, '0');
      const minutes = String(now.getMinutes()).padStart(2, '0');
      const seconds = String(now.getSeconds()).padStart(2, '0');
      this.currentTime = `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`;
    },
    // 从本地存储获取用户信息
    async getUserInfoFromStore() {
      try {
        // 使用 electron API 从本地存储获取用户信息
        const userInfo = await storage.local.get('userInfo');
        console.log('userInfo',userInfo)
        if (userInfo && userInfo.account) {
          this.username = userInfo.account;
          this.realName = userInfo.realName;
        }
      } catch (error) {
        console.error('获取用户信息失败:', error);
      }
    },
  }
};
</script>

<style scoped>
.user-info-bar {
  background: rgba(28, 31, 55, 0.7);
  border-radius: 4px;
  padding: 8px 12px;
}

.user-info-wrapper {
  position: fixed;
  bottom: 20px;
  right: 20px;
  z-index: 100;
}

/* 自适应不同屏幕分辨率 */
@media screen and (min-width: 1920px) {
  .user-info-bar {
    padding: 10px 15px;
  }
  
  .user-info-wrapper {
    bottom: 30px;
    right: 30px;
  }
}

@media screen and (max-width: 1366px) {
  .user-info-bar {
    padding: 7px 10px;
  }
  
  .user-info-wrapper {
    bottom: 15px;
    right: 15px;
  }
}

@media screen and (max-width: 1024px) {
  .user-info-bar {
    padding: 6px 8px;
  }
  
  .user-info-wrapper {
    bottom: 10px;
    right: 10px;
  }
}
</style> 