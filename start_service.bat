@echo off
echo ========================================
echo           启动 icbBox 服务
echo ========================================
echo.

REM 检查服务进程是否已经在运行
tasklist /FI "IMAGENAME eq document_cabinet.exe" | find "document_cabinet.exe" > nul
if %errorlevel% equ 0 (
  echo 服务已经在运行中，无需重复启动。
  echo.
  echo 进程信息：
  tasklist /FI "IMAGENAME eq document_cabinet.exe" /FO TABLE
) else (
  echo 正在启动服务...
  
  REM 确定安装目录下的wsServer路径
  set "wsServerPath=%~dp0resources\wsServer"
  
  if exist "%wsServerPath%\document_cabinet.exe" (
    echo 找到服务程序：%wsServerPath%\document_cabinet.exe
    
    REM 切换到wsServer目录
    cd /d "%wsServerPath%"
    
    REM 启动服务程序
    start "" "%wsServerPath%\document_cabinet.exe"
    
    echo 服务启动请求已发送，正在检查服务状态...
    timeout /t 3 > nul
    
    REM 再次检查服务是否成功启动
    tasklist /FI "IMAGENAME eq document_cabinet.exe" | find "document_cabinet.exe" > nul
    if %errorlevel% equ 0 (
      echo 服务已成功启动！
    ) else (
      echo 服务启动失败，请检查日志文件获取更多信息。
      echo 日志路径：%wsServerPath%\log\
    )
  ) else (
    echo 错误：找不到服务程序。
    echo 已检查路径：%wsServerPath%\document_cabinet.exe
    echo.
    echo 请确保安装完整且路径正确。
  )
)

echo.
echo 按任意键退出...
pause > nul 