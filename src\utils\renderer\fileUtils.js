/**
 * 渲染进程文件操作工具类
 */
class FileUtils {
  /**
   * 检查 API 是否可用
   */
  static checkAPI() {
    if (!window.electronAPI) {
      throw new Error('electronAPI 未初始化，请检查 preload.js 是否正确加载')
    }
  }

  /**
   * 读取文件内容
   * @param {string} filePath 文件路径
   * @returns {Promise<string>} 文件内容
   */
  static async readFile(filePath) {
    this.checkAPI()
    return await window.electronAPI.readFile(filePath)
  }

  /**
   * 写入文件内容
   * @param {string} filePath 文件路径
   * @param {string} content 文件内容
   * @returns {Promise<boolean>}
   */
  static async writeFile(filePath, content) {
    this.checkAPI()
    return await window.electronAPI.writeFile(filePath, content)
  }

  /**
   * 选择文件
   * @param {Object} options 选项配置
   * @returns {Promise<string[]>} 选中的文件路径数组
   */
  static async selectFile(options) {
    this.checkAPI()
    return await window.electronAPI.selectFile(options)
  }

  /**
   * 选择保存路径
   * @param {Object} options 选项配置
   * @returns {Promise<string>} 保存路径
   */
  static async selectSavePath(options) {
    this.checkAPI()
    return await window.electronAPI.selectSavePath(options)
  }
}

export default FileUtils 