/* Element UI 自定义主题颜色 */

/* 主要按钮颜色 */
.el-button--primary {
  background-color: #0066ff !important;
  border-color: #0066ff !important;
}
.el-button--primary:hover,
.el-button--primary:focus {
  background-color: #1a75ff !important;
  border-color: #1a75ff !important;
}
.el-button--primary:active {
  background-color: #0052cc !important;
  border-color: #0052cc !important;
}

/* 文字按钮颜色 */
.el-button--text {
  color: #0066ff !important;
}
.el-button--text:hover,
.el-button--text:focus {
  color: #1a75ff !important;
}
.el-button--text:active {
  color: #0052cc !important;
}

/* 其他主题相关组件颜色 */
.el-pagination__button:not([disabled]):hover,
.el-pagination .el-pager li:not(.disabled).active {
  color: #0066ff !important;
}

.el-checkbox__input.is-checked .el-checkbox__inner,
.el-checkbox__input.is-indeterminate .el-checkbox__inner,
.el-radio__input.is-checked .el-radio__inner,
.el-switch.is-checked .el-switch__core {
  background-color: #0066ff !important;
  border-color: #0066ff !important;
}

.el-dropdown-menu__item:focus, 
.el-dropdown-menu__item:not(.is-disabled):hover {
  background-color: #e6f0ff !important;
  color: #0066ff !important;
}

/* 输入框聚焦时的边框颜色 */
.el-input__inner:focus,
.el-textarea__inner:focus {
  border-color: #0066ff !important;
}

/* 进度条颜色 */
.el-progress-bar__inner {
  background-color: #0066ff !important;
}

/* 选中的表格行 */
.el-table__body tr.current-row>td {
  background-color: rgba(0, 102, 255, 0.1) !important;
}

/* 日期选择器和时间选择器 */
.el-date-table td.today span,
.el-date-table td span:hover,
.el-date-picker__header-label:hover,
.el-time-panel__btn.confirm {
  color: #0066ff !important;
}
.el-date-table td.current:not(.disabled) span {
  background-color: #0066ff !important;
} 