class WebSocketService {
  static instance = null;
  static get Instance() {
    if (!this.instance) {
      this.instance = new WebSocketService();
    }
    return this.instance;
  }

  ws = null;
  status = false;
  pingTimer = null;
  pingTime = 10000; // 心跳间隔 10s
  reconnectCount = 0;
  maxReconnectCount = 5;
  reconnectTimer = null;
  currentUrl = '';

  constructor() {
    // 构造函数不再监听网络状态
  }

  // 连接 WebSocket
  connect(url, params = {}) {
    if (this.ws) {
      this.ws.close();
    }
    
    try {
      // 处理URL参数
      let fullUrl = url;
      const queryParams = new URLSearchParams();
      
      // 添加所有参数到查询字符串
      Object.entries(params).forEach(([key, value]) => {
        queryParams.append(key, value);
      });
      
      // 如果有参数，添加到URL
      const queryString = queryParams.toString();
      if (queryString) {
        fullUrl += `?${queryString}`;
      }
      
      this.currentUrl = fullUrl;
      this.ws = new WebSocket(fullUrl);
      this.initEventHandle();
      return true;
    } catch (e) {
      console.error('WebSocket连接异常:', e);
      this.handleError(e);
      return false;
    }
  }

  // 初始化事件处理
  initEventHandle() {
    console.log('初始化WebSocket事件处理...');
    
    this.ws.onopen = () => {
      this.status = true;
      this.reconnectCount = 0;
      this.startHeartbeat();
      console.log('WebSocket连接成功');
      window.dispatchEvent(new CustomEvent('ws-connect', { detail: {} }));
    }

    this.ws.onclose = (event) => {
      this.status = false;
      this.stopHeartbeat();
      console.log('WebSocket连接关闭', event.code, event.reason);
      this.handleClose(event);
    }

    this.ws.onerror = (error) => {
      this.status = false;
      this.stopHeartbeat();
      console.error('WebSocket连接错误:', error);
      this.handleError(error);
    }

    this.ws.onmessage = (event) => {
      try {
        const data = JSON.parse(event.data);
        this.handleMessage(data);
      } catch (e) {
        console.error('消息解析错误:', e);
      }
    }
  }

  // 处理错误
  handleError(error) {
    this.status = false;
    this.stopHeartbeat();
    if (this.ws) {
      this.ws.close();
    }
    // 触发错误事件
    window.dispatchEvent(new CustomEvent('ws-error', { detail: error }));
    // 添加自动重连
    this.reconnect();
  }

  // 处理连接关闭
  handleClose(event) {
    this.status = false;
    this.stopHeartbeat();
    // 触发关闭事件
    window.dispatchEvent(new CustomEvent('ws-close', { detail: event }));
    // 添加自动重连
     this.reconnect();
  }

  // 发送消息
  send(data) {
    if (this.status && this.ws && this.ws.readyState === WebSocket.OPEN) {
      try {
        this.ws.send(JSON.stringify({...data,id:`${data.id || 'PING'}_${new Date().getTime()}`, timestamp: new Date().toLocaleString('zh-CN', {year: 'numeric', month: '2-digit', day: '2-digit', hour: '2-digit', minute: '2-digit', second: '2-digit'}).replace(/\//g, '-')}));
        return true;
      } catch (e) {
        console.error('发送消息失败:', e);
        this.handleError(e);
        return false;
      }
    }
    return false;
  }

  // 处理接收到的消息
  handleMessage(data) {
    // console.log("111111111111111111",data);
    // if (data.type === 'ping') {

    //   console.log("pong")
    //   this.send({ type: 'pong' });
    //   return;
    // }
    // 触发消息事件

    window.dispatchEvent(new CustomEvent('ws-message', { detail: data }));
  }

  // 开始心跳
  startHeartbeat() {
    console.log('启动心跳检测...');
    this.stopHeartbeat();
    this.pingTimer = setInterval(() => {
      if (this.status) {
        this.send({ type: 10000 });
        // console.log('心跳...');
      }
    }, this.pingTime);
  }

  // 停止心跳
  stopHeartbeat() {
    if (this.pingTimer) {
      clearInterval(this.pingTimer);
      this.pingTimer = null;
    }
  }

  // 重连
  reconnect() {
    // if (this.reconnectCount >= this.maxReconnectCount) {
    //   console.log('重连次数超过限制');
    //   window.dispatchEvent(new CustomEvent('ws-max-reconnect'));
    //   return;
    // }

    if (this.reconnectTimer) {
      clearTimeout(this.reconnectTimer);
    }

    this.reconnectTimer = setTimeout(() => {
      this.reconnectCount++;
      console.log(`第${this.reconnectCount}次重连...`);
      if (this.currentUrl) {
        this.connect(this.currentUrl);
      }
    }, 3000);
  }

  // 关闭连接
  close() {
    this.stopHeartbeat();
    if (this.reconnectTimer) {
      clearTimeout(this.reconnectTimer);
    }
    if (this.ws) {
      this.ws.close();
    }
  }

  // 设置配置参数
  setConfig(config) {
    if (config.pingInterval) {
      this.pingTime = config.pingInterval;
    }
    if (config.maxReconnectCount) {
      this.maxReconnectCount = config.maxReconnectCount;
    }
  }
}

export default WebSocketService.Instance; 