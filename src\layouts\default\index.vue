<template>
  <div class="layout-container">
    <header class="header">
      <div class="logo">后台管理系统</div>
      <div class="user-info">
        <span>{{ userInfo.name }}</span>
        <button @click="handleLogout">退出</button>
      </div>
    </header>
    <div class="main-container">
      <aside class="sidebar">
        <nav>
          <router-link to="/dashboard">首页</router-link>
          <!-- 其他菜单项 -->
        </nav>
      </aside>
      <main class="content">
        <router-view />
      </main>
    </div>
  </div>
</template>

<script>
import { getUserInfo } from '@/api/user'

export default {
  name: 'DefaultLayout',
  data() {
    return {
      userInfo: {
        name: '用户'
      }
    }
  },
  created() {
    this.fetchUserInfo()
  },
  methods: {
    async fetchUserInfo() {
      try {
        const data = await getUserInfo()
        this.userInfo = data
      } catch (error) {
        console.error('获取用户信息失败:', error)
      }
    },
    handleLogout() {
      localStorage.removeItem('token')
      this.$router.push('/login')
    }
  }
}
</script>

<style scoped>
.layout-container {
  min-height: 100vh;
  display: flex;
  flex-direction: column;
}

.header {
  height: 60px;
  background-color: #fff;
  box-shadow: 0 1px 4px rgba(0,21,41,.08);
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0 20px;
}

.main-container {
  flex: 1;
  display: flex;
}

.sidebar {
  width: 200px;
  background-color: #304156;
  padding: 20px 0;
}

.sidebar a {
  display: block;
  color: #fff;
  padding: 10px 20px;
  text-decoration: none;
}

.sidebar a.router-link-active {
  background-color: #263445;
}

.content {
  flex: 1;
  padding: 20px;
  background-color: #f0f2f5;
}

.user-info {
  display: flex;
  align-items: center;
  gap: 10px;
}

.user-info button {
  padding: 5px 15px;
  border: none;
  background-color: #f56c6c;
  color: white;
  border-radius: 4px;
  cursor: pointer;
}
</style> 