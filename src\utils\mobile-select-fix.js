/**
 * 修复移动设备上 Element UI Select 组件键盘弹出导致的卡顿问题
 * 这个文件提供了两种解决方案：
 * 1. 监听输入事件，在移动设备上阻止 Select 搜索框的输入（不显示键盘）
 * 2. 优化下拉框在移动设备上的渲染方式
 */

// 判断当前是否为移动设备
const isMobileDevice = () => {
  return /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent);
};

/**
 * 为所有 el-select 组件添加移动设备优化
 * 在组件挂载后调用
 */
export const fixMobileSelect = () => {
  if (!isMobileDevice()) return;
  
  // 延迟执行，确保 DOM 已完全渲染
  setTimeout(() => {
    // 方案一：禁用搜索框输入，阻止键盘弹出
    const disableKeyboard = () => {
      const selectInputs = document.querySelectorAll('.el-select .el-input__inner');
      selectInputs.forEach(input => {
        input.setAttribute('readonly', 'readonly');
        
        // 点击时触发下拉框显示，但不显示键盘
        input.addEventListener('click', (e) => {
          // 阻止默认行为，防止弹出键盘
          e.preventDefault();
          // 手动触发 el-select 的展开
          const selectComponent = input.closest('.el-select');
          if (selectComponent) {
            // 查找 Vue 实例并手动触发下拉框
            const vueInstance = selectComponent.__vue__;
            if (vueInstance) {
              vueInstance.visible = true;
            }
          }
        });
      });
    };
    
    // 方案二：优化下拉框渲染
    const optimizeDropdown = () => {
      // 监听所有下拉框出现
      const observer = new MutationObserver((mutations) => {
        mutations.forEach((mutation) => {
          if (mutation.addedNodes.length) {
            mutation.addedNodes.forEach((node) => {
              if (node.classList && node.classList.contains('el-select-dropdown')) {
                // 优化下拉框位置，防止键盘弹出导致位置错误
                node.style.position = 'fixed';
                node.style.maxHeight = '40vh'; // 限制高度为视口的40%
                
                // 使用 transform 优化渲染性能
                node.style.transform = 'translateZ(0)';
                node.style.webkitTransform = 'translateZ(0)';
              }
            });
          }
        });
      });
      
      // 监听 body 的子节点变化，因为 el-select-dropdown 被挂载到 body 上
      observer.observe(document.body, { childList: true, subtree: false });
    };
    
    // 执行优化
    disableKeyboard();
    optimizeDropdown();
    
    // 添加全局样式优化
    addMobileSelectStyles();
  }, 500);
};

/**
 * 添加全局样式优化移动设备上的 Select 组件
 */
const addMobileSelectStyles = () => {
  const styleElement = document.createElement('style');
  styleElement.textContent = `
    /* 移动设备上的 Select 组件样式优化 */
    @media (max-width: 768px) {
      .el-select-dropdown {
        position: fixed !important;
        max-height: 40vh !important;
        overflow-y: auto !important;
        -webkit-overflow-scrolling: touch !important;
        transform: translateZ(0) !important;
        -webkit-transform: translateZ(0) !important;
      }
      
      .el-select-dropdown__item {
        padding: 12px 20px !important; /* 增大点击区域 */
      }
      
      .el-select .el-input__inner {
        -webkit-appearance: none !important; /* 禁用原生外观 */
      }
      
      /* 防止输入时页面跳动 */
      body.el-select-dropdown-visible {
        height: 100% !important;
        overflow: hidden !important;
      }
    }
  `;
  document.head.appendChild(styleElement);
  
  // 添加下拉框显示时的 body 类
  document.addEventListener('click', (e) => {
    if (e.target.closest('.el-select')) {
      document.body.classList.add('el-select-dropdown-visible');
    } else if (!e.target.closest('.el-select-dropdown')) {
      document.body.classList.remove('el-select-dropdown-visible');
    }
  }, true);
};

export default fixMobileSelect; 