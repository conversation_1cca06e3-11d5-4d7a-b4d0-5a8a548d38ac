<!-- 用户添加/编辑对话框组件 -->
<template>
  <el-dialog fullscreen :title="dialogTitle" :visible.sync="dialogVisibleValue" width="40%" custom-class="group-dialog" @close="handleClose">
    <div class="dialog-container">
      <SoftKeyboard :autoInitialize="false"></SoftKeyboard>
      <el-form :model="form" :rules="rules" ref="form" label-width="120px" class="form-content">

        <el-form-item label="用户名" prop="account">
          <el-input v-model="form.account" placeholder="请输入用户名"></el-input>
        </el-form-item>
        
        <el-form-item label="姓名" prop="realName">
          <el-input v-model="form.realName" placeholder="请输入姓名"></el-input>
        </el-form-item>
        
        <el-form-item label="手机号" prop="mobile">
          <el-input v-model="form.mobile" placeholder="请输入手机号"></el-input>
        </el-form-item>

        <el-form-item label="证件ID" prop="card">
          <el-input v-model="form.card" placeholder="请输入证件ID"></el-input>
        </el-form-item>

        <el-form-item label="公司" prop="companyId">
          <el-cascader
                v-model="form.companyId"
                :options="organizationtreedata"
                :props="{
                  value: 'id',
                  label: 'organizationName',
                  children: 'children',
                  checkStrictly: true,
                  emitPath: false,
                }"

                clearable
                style="width: 100%"
                size="small"
                placeholder="请选择公司"
                @change="
                  (val) => {
                    if (val) {
                      const findNode = (nodes, id) => {
                        for (let node of nodes) {
                          if (node.id === id) {
                            return node;
                          }
                          if (node.children) {
                            const found = findNode(node.children, id);
                            if (found) return found;
                          }
                        }
                        return null;
                      };
                      const node = findNode(organizationtreedata, val);
                      form.organizationName = node.organizationName;
                    } else {
                      form.organizationName = '';
                    }
                  }
                "
              >
              </el-cascader>
        </el-form-item>
        
        <el-form-item label="所属部门" prop="belongDeptId">
          <el-cascader
                v-model="form.belongDeptId"
                :options="organizationtreedata"
                :props="{
                  value: 'id',
                  label: 'organizationName',
                  children: 'children',
                  checkStrictly: true,
                  emitPath: false,
                }"

                clearable
                style="width: 100%"
                size="small"
                placeholder="请选择公司"
                @change="
                  (val) => {
                    if (val) {
                      const findNode = (nodes, id) => {
                        for (let node of nodes) {
                          if (node.id === id) {
                            return node;
                          }
                          if (node.children) {
                            const found = findNode(node.children, id);
                            if (found) return found;
                          }
                        }
                        return null;
                      };
                      const node = findNode(organizationtreedata, val);
                      form.organizationName = node.organizationName;
                    } else {
                      form.organizationName = '';
                    }
                  }
                "
              >
              </el-cascader>
        </el-form-item>
        
        <el-form-item label="工作部门" prop="workDeptId">
          <el-cascader
                v-model="form.workDeptId"
                :options="organizationtreedata"
                :props="{
                  value: 'id',
                  label: 'organizationName',
                  children: 'children',
                  checkStrictly: true,
                  emitPath: false,
                }"

                clearable
                style="width: 100%"
                size="small"
                placeholder="请选择公司"
                @change="
                  (val) => {
                    if (val) {
                      const findNode = (nodes, id) => {
                        for (let node of nodes) {
                          if (node.id === id) {
                            return node;
                          }
                          if (node.children) {
                            const found = findNode(node.children, id);
                            if (found) return found;
                          }
                        }
                        return null;
                      };
                      const node = findNode(organizationtreedata, val);
                      form.organizationName = node.organizationName;
                    } else {
                      form.organizationName = '';
                    }
                  }
                "
              >
              </el-cascader>
        </el-form-item>


        <el-form-item label="用户角色" prop="roleIds">
          <el-select
            v-mobile-select
            style="width: 100%;"
            v-model="form.roleIds"
            multiple
            size="small"
            :disabled="isCreateAdminMode"
            placeholder="请选择用户角色">
            <el-option v-for="item in roleOptions" :key="item.id" :label="item.roleName" :value="item.id"></el-option>
          </el-select>
          <div v-if="isCreateAdminMode" class="role-tip">
            <i class="el-icon-info"></i>
            <span>创建管理员模式下，用户角色固定为超级管理员</span>
          </div>
        </el-form-item>


        <el-form-item label="性别">
            <el-radio-group
              v-model="form.gender"

            >
              <el-radio :label="'1'">男</el-radio>
              <el-radio :label="'0'">女</el-radio>
            </el-radio-group>
          </el-form-item>



        
        <el-form-item label="状态" prop="status">
          <el-select 
            v-mobile-select
            v-model="form.status" 
            size="small" 
            placeholder="请选择用户状态">
            <el-option label="正常" value="1"></el-option>
            <el-option label="锁定" value="0"></el-option>
          </el-select>
        </el-form-item>

        <el-form-item label="是否自由存取" prop="">
          <el-select 
            v-mobile-select
            v-model="form.isFreeAccess" 
            size="small" 
            placeholder="是否自由存取">
            <el-option label="是" value="1"></el-option>
            <el-option label="否" value="0"></el-option>
          </el-select>
        </el-form-item>

      </el-form>
      <div class="dialog-footer">
        <el-button @click="handleCancel">取 消</el-button>
        <el-button type="primary" @click="submitForm">确 定</el-button>
      </div>
    </div>
  </el-dialog>
</template>

<script>
import SoftKeyboard from "@/components/SoftKeyboard.vue";
export default {
  name: 'GroupDialog',
  components:{
    SoftKeyboard
  },
  props: {
    // 对话框标题
    dialogTitle: {
      type: String,
      default: '用户信息'
    },
    // 对话框是否可见
    dialogVisible: {
      type: Boolean,
      default: false
    },
    organizationtreedata:{
       type:Array,
       default:()=>{
        return []
       }
    },
    roleOptions:{
      type:Array,
      default:()=>{
        return []
      }
    },
    // 用户表单数据
    groupData: {
      type: Object,
      default: () => ({
        account: '',
        realName: '',
        mobile: '',
        card:'',
        companyId: '',
        belongDeptId: '',
        workDeptId: '',
        status: '1',
        isFreeAccess: '0',
        roleIds: []
      })
    },
    // 是否为创建管理员模式
    isCreateAdminMode: {
      type: Boolean,
      default: false
    },
    // 是否为编辑模式
    isEditMode: {
      type: Boolean,
      default: false
    }
  },
  data() {
    // 手机号验证规则
    return {
      // 表单数据
      form: {
        account: '',
        realName: '',
        mobile: '',
        card:'',
        companyId: '',
        belongDeptId: '',
        workDeptId: '',
        status: '1',
        isFreeAccess: '0',
        roleIds: []
      },
      // 角色选项
   
      // 表单验证规则
      rules: {
        account: [
          {required: true, message: "请输入用户名", trigger: "blur"},
          {
            min: 3,
            max: 15,
            message: "长度在 3 到 16 个字符",
            trigger: "blur",
          },
          {
            pattern: /^[a-z0-9_\-!]+$/,
            message: "只能包含小写字母、数字、下划线_、连字符-、感叹号!",
            trigger: "blur"
          },
        ],
        realName: [{required: true, message: "请输入姓名", trigger: "blur"},
           {min: 2, max: 16, message: "长度在 2 到 16 个字符", trigger: "blur"}
        ],
        nickName: [{required: true, message: "请输入姓名", trigger: "blur"}],
        mobile: [{required: true, message: "请输入手机号", trigger: "blur"},
          {
            pattern: /^1[3-9]\d{9}$/,
            message: "请输入正确的手机号格式",
            trigger: "blur"
          }
        ],
        card: [{required: true, message: "请输入证件ID", trigger: "blur"}
        ], 
        organizationId: [
          {required: true, message: "请选择组织", trigger: "change"},
        ],
        companyId: [
          {required: true, message: "请选择公司", trigger: "change"},
        ],
        belongDeptId: [
          {required: true, message: "请选择所属部门", trigger: "change"},
        ],
        workDeptId: [
          {required: true, message: "请选择工作部门", trigger: "change"},
        ],
        roleIds: [
          {required: true, message: "请选择用户角色", trigger: "change", type: "array"},
        ],
      }
    };
  },
  computed: {
    // 计算属性处理dialogVisible，避免直接修改prop
    dialogVisibleValue: {
      get() {
        return this.dialogVisible;
      },
      set(value) {
        this.$emit('update:dialogVisible', value);
      }
    },
    // 获取超级管理员角色
    superAdminRole() {
      return this.roleOptions.find(role =>
        role.roleName === '超级管理员' ||
        role.roleCode === 'super_admin' ||
        role.roleCode === 'SUPER_ADMIN' ||
        role.roleName.includes('超级管理员')
      );
    }
  },
  watch: {
    // 监听传入的用户数据变化
    groupData: {
      handler(newVal) {
        if (newVal) {
          this.form = JSON.parse(JSON.stringify(newVal));


        }
      },
      immediate: true,
      deep: true
    },
    // 监听角色选项变化，在创建管理员模式下自动设置超级管理员角色
    roleOptions: {
      handler(newVal) {
        if (newVal && newVal.length > 0 && this.isCreateAdminMode) {
          this.setSuperAdminRole();
        }
      },
      immediate: true
    },
    // 监听创建管理员模式变化
    isCreateAdminMode: {
      handler(newVal) {
        if (newVal && this.roleOptions && this.roleOptions.length > 0) {
          this.setSuperAdminRole();
        }
      },
      immediate: true
    }
  },
  methods: {
    // 设置超级管理员角色
    setSuperAdminRole() {
      // 只在创建管理员模式且非编辑模式下自动设置超级管理员角色
      if (this.isCreateAdminMode && !this.isEditMode && this.superAdminRole) {
        this.form.roleIds = [this.superAdminRole.id];
      }
    },
    // 提交表单
    submitForm() {
      this.$refs.form.validate(valid => {
        if (valid) {
          this.$emit('submit', this.form);
        }
      });
    },
    // 取消操作
    handleCancel() {
      this.$emit('update:dialogVisible', false);
    },
    // 关闭对话框时
    handleClose() {
      this.$refs.form.resetFields();
      this.$emit('update:dialogVisible', false);
    }
  }
};
</script>

<style scoped>
/* 角色提示样式 */
.role-tip {
  margin-top: 5px;
  color: #E6A23C;
  font-size: 12px;
  display: flex;
  align-items: center;
}

.role-tip i {
  margin-right: 4px;
}

/* 对话框样式 */
:deep(.el-dialog) {
  background-color: #1C1F37 !important;
  border: 1px solid rgba(65, 105, 225, 0.5) !important;
  border-radius: 12px !important;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.5) !important;
  overflow: hidden !important;
  display: flex !important;
  flex-direction: column !important;
  height: 100% !important;
}

:deep(.el-dialog__header) {
  background-color: rgba(65, 105, 225, 0.2) !important;
  padding: 15px 20px !important;
  border-bottom: 1px solid rgba(65, 105, 225, 0.3) !important;
}

:deep(.el-dialog__title) {
  color: #fff !important;
  font-weight: bold !important;
}

:deep(.el-dialog__body) {
  color: #fff !important;
  padding: 0 !important;
  flex: 1 !important;
  overflow: auto !important;
}

.dialog-container {
  display: flex;
  flex-direction: column;
  height: 100%;
}

.form-content {
  flex: 1;
  overflow-y: auto;
  padding: 20px;
}

.dialog-footer {
  border-top: 1px solid rgba(65, 105, 225, 0.3) !important;
  padding: 15px 20px !important;
  position: sticky;
  bottom: 0;
  background-color: #1C1F37;
  display: flex;
  justify-content: flex-end;
  z-index: 10;
}

:deep(.el-form-item__label) {
  color: #fff !important;
}

:deep(.el-input__inner), :deep(.el-select .el-input__inner) {
  background-color: rgba(0, 0, 0, 0.3) !important;
  border-color: rgba(65, 105, 225, 0.5) !important;
  color: #fff !important;
  transition: all 0.3s ease !important;
}

:deep(.el-input__inner:focus), :deep(.el-select .el-input__inner:focus) {
  border-color: rgba(65, 105, 225, 0.8) !important;
  box-shadow: 0 0 10px rgba(65, 105, 225, 0.3) !important;
}

:deep(.el-select-dropdown__item) {
  color: #333 !important;
}

:deep(.el-select-dropdown__item.selected) {
  color: #409EFF !important;
}

:deep(.el-button) {
  border-radius: 4px !important;
  transition: all 0.3s ease !important;
}

:deep(.el-button--primary) {
  background-color: rgba(65, 105, 225, 0.8) !important;
  border-color: rgba(65, 105, 225, 0.8) !important;
  box-shadow: 0 4px 10px rgba(65, 105, 225, 0.3) !important;
}

:deep(.el-button--primary:hover) {
  background-color: rgba(65, 105, 225, 1) !important;
  transform: translateY(-2px);
  box-shadow: 0 6px 14px rgba(65, 105, 225, 0.4) !important;
}

/* 移动设备适配 */
@media (max-width: 768px) {
  .form-content {
    padding: 15px;
  }
  
  :deep(.el-form-item) {
    margin-bottom: 15px;
  }
  
  :deep(.el-form-item__label) {
    padding-bottom: 5px;
  }
  
  :deep(.el-select-dropdown__item) {
    padding: 10px 15px;
  }
}
</style> 