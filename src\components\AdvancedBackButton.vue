<!-- 高级返回按钮组件 -->
<template>
  <div class="advanced-back-button">
    <!-- 返回按钮 -->
    <button
      class="back-btn"
      :class="[buttonSize, buttonType, { 'with-animation': withAnimation }]"
      @click="handleClick"
      :disabled="disabled"
    >
      <!-- 图标 -->
      <i :class="iconClass" v-if="showIcon"></i>
      
      <!-- 按钮文本 -->
      <span v-if="buttonText" class="btn-text">{{ buttonText }}</span>
    </button>
    
    <!-- 确认对话框 -->
    <el-dialog
      :visible.sync="dialogVisible"
      :title="confirmTitle"
      width="30%"
      :append-to-body="true"
      :close-on-click-modal="false"
      :show-close="true"
    >
      <span>{{ confirmMessage }}</span>
      <span slot="footer" class="dialog-footer">
        <el-button @click="dialogVisible = false">{{ cancelText }}</el-button>
        <el-button type="primary" @click="confirmBack">{{ confirmText }}</el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
export default {
  name: 'AdvancedBackButton',
  props: {
    // 按钮文本
    buttonText: {
      type: String,
      default: '返回'
    },
    // 按钮大小: small, medium, large
    buttonSize: {
      type: String,
      default: 'medium',
      validator: value => ['small', 'medium', 'large'].includes(value)
    },
    // 按钮类型: default, primary, success, warning, danger
    buttonType: {
      type: String,
      default: 'default',
      validator: value => ['default', 'primary', 'success', 'warning', 'danger'].includes(value)
    },
    // 是否显示图标
    showIcon: {
      type: Boolean,
      default: true
    },
    // 图标类名
    iconClass: {
      type: String,
      default: 'fas fa-arrow-left'
    },
    // 是否禁用
    disabled: {
      type: Boolean,
      default: false
    },
    // 是否需要确认
    needConfirm: {
      type: Boolean,
      default: false
    },
    // 确认标题
    confirmTitle: {
      type: String,
      default: '确认返回'
    },
    // 确认消息
    confirmMessage: {
      type: String,
      default: '确定要返回吗？未保存的数据将会丢失。'
    },
    // 确认按钮文本
    confirmText: {
      type: String,
      default: '确认'
    },
    // 取消按钮文本
    cancelText: {
      type: String,
      default: '取消'
    },
    // 是否启用动画
    withAnimation: {
      type: Boolean,
      default: true
    },
    // 指定返回的路由名称，如果不指定则返回上一页
    routeName: {
      type: String,
      default: ''
    },
    // 路由参数
    routeParams: {
      type: Object,
      default: () => ({})
    },
    // 路由查询参数
    routeQuery: {
      type: Object,
      default: () => ({})
    },
    // 返回前的回调函数，如果返回false则阻止返回
    beforeBack: {
      type: Function,
      default: () => true
    }
  },
  data() {
    return {
      // 对话框是否可见
      dialogVisible: false,
      // 上次点击时间，用于防止快速连续点击
      lastClickTime: 0
    };
  },
  methods: {
    // 处理点击事件
    handleClick() {
      const now = new Date().getTime();
      // 防止快速连续点击（300ms内）
      if (now - this.lastClickTime < 300) {
        console.log("点击过于频繁，忽略此次点击");
        return;
      }
      this.lastClickTime = now;
      
      // 发送点击事件
      this.$emit('click');
      
      // 执行返回前的回调
      const canProceed = this.beforeBack();
      if (!canProceed) {
        return;
      }
      
      // 如果需要确认，显示确认对话框
      if (this.needConfirm) {
        this.dialogVisible = true;
      } else {
        // 否则直接返回
        this.goBack();
      }
    },
    
    // 确认返回
    confirmBack() {
      this.dialogVisible = false;
      this.goBack();
    },
    
    // 返回上一页或指定页面
    goBack() {
      // 发送返回事件
      this.$emit('back');
      
      // 如果指定了路由名称，则跳转到指定页面
      if (this.routeName) {
        this.$router.push({
          name: this.routeName,
          params: this.routeParams,
          query: this.routeQuery
        });
      } else {
        // 否则返回上一页
        this.$router.back();
      }
    }
  }
};
</script>

<style scoped>
.advanced-back-button {
  display: inline-block;
}

.back-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  border: none;
  cursor: pointer;
  border-radius: 4px;
  transition: all 0.3s;
  padding: 0 15px;
  font-weight: 500;
}

.back-btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

/* 按钮大小 */
.small {
  height: 32px;
  font-size: 12px;
}

.medium {
  height: 40px;
  font-size: 14px;
}

.large {
  height: 48px;
  font-size: 16px;
}

/* 按钮类型 */
.default {
  background-color: #f5f5f5;
  color: #333;
}

.primary {
  background-color: #409EFF;
  color: white;
}

.success {
  background-color: #67C23A;
  color: white;
}

.warning {
  background-color: #E6A23C;
  color: white;
}

.danger {
  background-color: #F56C6C;
  color: white;
}

/* 按钮悬停效果 */
.default:hover:not(:disabled) {
  background-color: #e8e8e8;
}

.primary:hover:not(:disabled) {
  background-color: #66b1ff;
}

.success:hover:not(:disabled) {
  background-color: #85ce61;
}

.warning:hover:not(:disabled) {
  background-color: #ebb563;
}

.danger:hover:not(:disabled) {
  background-color: #f78989;
}

/* 图标和文本间距 */
i + .btn-text {
  margin-left: 5px;
}

/* 动画效果 */
.with-animation {
  position: relative;
  overflow: hidden;
}

.with-animation::after {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  width: 5px;
  height: 5px;
  background: rgba(255, 255, 255, 0.5);
  opacity: 0;
  border-radius: 100%;
  transform: scale(1, 1) translate(-50%);
  transform-origin: 50% 50%;
}

.with-animation:focus:not(:active)::after {
  animation: ripple 0.6s ease-out;
}

@keyframes ripple {
  0% {
    transform: scale(0, 0);
    opacity: 0.5;
  }
  20% {
    transform: scale(25, 25);
    opacity: 0.5;
  }
  100% {
    opacity: 0;
    transform: scale(40, 40);
  }
}
</style> 