/**
 * 浏览器本地存储工具类
 * 封装localStorage、sessionStorage和cookies的读写操作
 */

/**
 * localStorage操作
 * 持久化存储，不会随浏览器关闭而清除
 */
export const localStore = {
  /**
   * 设置localStorage
   * @param {string} key - 存储的键名
   * @param {any} value - 存储的值，会自动序列化为JSON字符串
   * @param {number} [expire] - 可选，过期时间（毫秒），不设置则永久有效
   */
  set(key, value, expire) {
    const data = {
      value,
      time: Date.now(),
      expire: expire ? Date.now() + expire : null
    };
    localStorage.setItem(key, JSON.stringify(data));
  },

  /**
   * 获取localStorage
   * @param {string} key - 存储的键名
   * @returns {any} 存储的值，如果已过期或不存在则返回null
   */
  get(key) {
    const dataStr = localStorage.getItem(key);
    if (!dataStr) return null;
    
    try {
      const data = JSON.parse(dataStr);
      // 判断是否过期
      if (data.expire && Date.now() > data.expire) {
        this.remove(key);
        return null;
      }
      return data.value;
    } catch (error) {
      console.error('获取localStorage数据解析错误:', error);
      return null;
    }
  },

  /**
   * 移除localStorage
   * @param {string} key - 存储的键名
   */
  remove(key) {
    localStorage.removeItem(key);
  },

  /**
   * 清空所有localStorage
   */
  clear() {
    localStorage.clear();
  }
};

/**
 * sessionStorage操作
 * 会话存储，随浏览器关闭而清除
 */
export const sessionStore = {
  /**
   * 设置sessionStorage
   * @param {string} key - 存储的键名
   * @param {any} value - 存储的值，会自动序列化为JSON字符串
   */
  set(key, value) {
    const data = {
      value,
      time: Date.now()
    };
    sessionStorage.setItem(key, JSON.stringify(data));
  },

  /**
   * 获取sessionStorage
   * @param {string} key - 存储的键名
   * @returns {any} 存储的值，如果不存在则返回null
   */
  get(key) {
    const dataStr = sessionStorage.getItem(key);
    if (!dataStr) return null;
    
    try {
      const data = JSON.parse(dataStr);
      return data.value;
    } catch (error) {
      console.error('获取sessionStorage数据解析错误:', error);
      return null;
    }
  },

  /**
   * 移除sessionStorage
   * @param {string} key - 存储的键名
   */
  remove(key) {
    sessionStorage.removeItem(key);
  },

  /**
   * 清空所有sessionStorage
   */
  clear() {
    sessionStorage.clear();
  }
};

/**
 * cookies操作
 */
export const cookieStore = {
  /**
   * 设置cookie
   * @param {string} key - 存储的键名
   * @param {string} value - 存储的值
   * @param {object} [options] - cookie选项
   * @param {number} [options.expire] - 过期时间（天），默认7天
   * @param {string} [options.path] - 路径，默认'/'
   * @param {string} [options.domain] - 域名
   * @param {boolean} [options.secure] - 是否只在https下传输
   */
  set(key, value, options = {}) {
    const { expire = 7, path = '/', domain, secure } = options;
    
    let cookie = `${encodeURIComponent(key)}=${encodeURIComponent(value)}`;
    
    // 设置过期时间
    if (expire) {
      const date = new Date();
      date.setDate(date.getDate() + expire);
      cookie += `; expires=${date.toUTCString()}`;
    }
    
    // 设置路径
    if (path) {
      cookie += `; path=${path}`;
    }
    
    // 设置域名
    if (domain) {
      cookie += `; domain=${domain}`;
    }
    
    // 设置安全标志
    if (secure) {
      cookie += '; secure';
    }
    
    document.cookie = cookie;
  },

  /**
   * 获取cookie
   * @param {string} key - 存储的键名
   * @returns {string|null} 存储的值，如果不存在则返回null
   */
  get(key) {
    const cookies = document.cookie.split('; ');
    for (let i = 0; i < cookies.length; i++) {
      const parts = cookies[i].split('=');
      const name = decodeURIComponent(parts[0]);
      if (name === key) {
        return decodeURIComponent(parts[1] || '');
      }
    }
    return null;
  },

  /**
   * 移除cookie
   * @param {string} key - 存储的键名
   * @param {object} [options] - cookie选项
   * @param {string} [options.path] - 路径，默认'/'
   * @param {string} [options.domain] - 域名
   */
  remove(key, options = {}) {
    const { path = '/', domain } = options;
    this.set(key, '', { expire: -1, path, domain });
  }
};

/**
 * 判断浏览器是否支持特定存储方式
 */
export const isSupported = {
  /**
   * 检查是否支持localStorage
   * @returns {boolean} 是否支持
   */
  localStorage() {
    try {
      const testKey = '__test_localStorage__';
      localStorage.setItem(testKey, testKey);
      localStorage.removeItem(testKey);
      return true;
    } catch (e) {
      return false;
    }
  },
  
  /**
   * 检查是否支持sessionStorage
   * @returns {boolean} 是否支持
   */
  sessionStorage() {
    try {
      const testKey = '__test_sessionStorage__';
      sessionStorage.setItem(testKey, testKey);
      sessionStorage.removeItem(testKey);
      return true;
    } catch (e) {
      return false;
    }
  },
  
  /**
   * 检查是否支持cookie
   * @returns {boolean} 是否支持
   */
  cookie() {
    return navigator.cookieEnabled;
  }
};

/**
 * 创建默认导出
 */
export default {
  local: localStore,
  session: sessionStore,
  cookie: cookieStore,
  isSupported
}; 